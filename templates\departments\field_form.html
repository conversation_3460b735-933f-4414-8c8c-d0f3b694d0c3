{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% if field %}تعديل حقل{% else %}إضافة حقل للقسم{% endif %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if field %}تعديل حقل{% else %}{% if group %}إضافة حقل لمجموعة "{{ group.name }}"{% else %}إضافة حقل للقسم{% endif %}{% endif %}</h1>
    <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى تفاصيل القسم
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if field %}تعديل حقل "{{ field.display_name }}"{% else %}{% if group %}إضافة حقل لمجموعة "{{ group.name }}" في قسم "{{ department.name }}"{% else %}إضافة حقل للقسم "{{ department.name }}"{% endif %}{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اسم الحقل في قاعدة البيانات (بالإنجليزية، بدون مسافات)</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.display_name.id_for_label }}">{{ form.display_name.label }}</label>
                        <input type="text" name="display_name" id="{{ form.display_name.id_for_label }}" class="form-control" value="{{ field.display_name|default:form.display_name.value|default:'' }}" required>
                        {% if form.display_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.display_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">الاسم الذي سيظهر للمستخدم</small>

                        <!-- إضافة حقل مخفي لاسم العرض -->
                        <input type="hidden" name="display_name_hidden" id="display_name_hidden" value="{{ field.display_name|default:form.display_name.value|default:'' }}">
                    </div>

                    <!-- إضافة كود JavaScript لتحديث الحقل المخفي عند تغيير اسم العرض -->
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const displayNameInput = document.getElementById('{{ form.display_name.id_for_label }}');
                            const displayNameHidden = document.getElementById('display_name_hidden');

                            displayNameInput.addEventListener('input', function() {
                                displayNameHidden.value = this.value;
                                console.log('Updated hidden field to:', this.value);
                            });
                        });
                    </script>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.group.id_for_label }}">{{ form.group.label }}</label>
                        {{ form.group }}
                        {% if form.group.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.group.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">المجموعة التي ينتمي إليها الحقل</small>
                        {% if group %}
                        <div class="alert alert-info mt-2">
                            <i class="fas fa-info-circle"></i> سيتم إضافة هذا الحقل إلى مجموعة "{{ group.name }}"
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.type.id_for_label }}">{{ form.type.label }}</label>
                        {{ form.type }}
                        {% if form.type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.type.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.order.id_for_label }}">{{ form.order.label }}</label>
                        {{ form.order }}
                        {% if form.order.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.order.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <div class="form-check mt-4">
                            {{ form.required }}
                            <label class="form-check-label" for="{{ form.required.id_for_label }}">
                                {{ form.required.label }}
                            </label>
                        </div>
                        {% if form.required.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.required.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="form-group" id="optionsGroup" style="display: none;">
                <label for="{{ form.options.id_for_label }}">{{ form.options.label }}</label>
                {{ form.options }}
                {% if form.options.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.options.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">أدخل الخيارات بتنسيق JSON، مثال: {"option1": "القيمة 1", "option2": "القيمة 2"}</small>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function updateFieldOptions() {
        const typeSelect = document.getElementById('id_type');
        const optionsGroup = document.getElementById('optionsGroup');

        if (typeSelect.value === 'select') {
            optionsGroup.style.display = 'block';
        } else {
            optionsGroup.style.display = 'none';
        }
    }

    // تحديث عند تغيير النوع
    document.getElementById('id_type').addEventListener('change', updateFieldOptions);

    // تحديث عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateFieldOptions();

        // إضافة مستمع للنموذج للتأكد من إرسال البيانات بشكل صحيح
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            // التأكد من أن حقل اسم العرض له قيمة
            const displayNameInput = document.getElementById('{{ form.display_name.id_for_label }}');
            if (!displayNameInput.value.trim()) {
                event.preventDefault();
                alert('يرجى إدخال اسم العرض');
                displayNameInput.focus();
                return false;
            }

            // طباعة قيمة اسم العرض للتصحيح
            console.log('Submitting form with display_name:', displayNameInput.value);

            // إضافة حقل مخفي للتأكد من إرسال اسم العرض
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'display_name_hidden';
            hiddenInput.value = displayNameInput.value;
            form.appendChild(hiddenInput);

            // التأكد من إرسال جميع الحقول الأخرى
            const requiredFields = ['name', 'type'];
            for (const fieldName of requiredFields) {
                const field = document.querySelector(`[name="${fieldName}"]`);
                if (field && !field.value.trim()) {
                    event.preventDefault();
                    alert(`يرجى إدخال ${fieldName === 'name' ? 'اسم الحقل' : 'نوع الحقل'}`);
                    field.focus();
                    return false;
                }
            }

            // إضافة معلومات تصحيح
            console.log('Form is valid, submitting...');
            console.log('Form data:', {
                name: document.querySelector('[name="name"]').value,
                display_name: displayNameInput.value,
                type: document.querySelector('[name="type"]').value,
                required: document.querySelector('[name="required"]')?.checked,
                group: document.querySelector('[name="group"]')?.value
            });

            return true;
        });
    });
</script>
{% endblock %}
