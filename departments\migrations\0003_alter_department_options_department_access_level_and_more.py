# Generated by Django 5.2 on 2025-04-24 11:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('departments', '0002_department_table_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['order', 'name'], 'verbose_name': 'قسم', 'verbose_name_plural': 'الأقسام'},
        ),
        migrations.AddField(
            model_name='department',
            name='access_level',
            field=models.CharField(choices=[('all', 'جميع المستخدمين'), ('admin', 'المشرفون فقط'), ('specific', 'مستخدمون محددون')], default='all', help_text='تحديد من يمكنه الوصول إلى هذا القسم', max_length=20, verbose_name='مستوى الوصول'),
        ),
        migrations.AddField(
            model_name='department',
            name='authorized_users',
            field=models.ManyToManyField(blank=True, related_name='accessible_departments', to=settings.AUTH_USER_MODEL, verbose_name='المستخدمون المصرح لهم'),
        ),
        migrations.AddField(
            model_name='department',
            name='color',
            field=models.CharField(default='#3498db', help_text='لون القسم في الواجهة', max_length=20, verbose_name='لون القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to=settings.AUTH_USER_MODEL, verbose_name='مدير القسم'),
        ),
        migrations.AddField(
            model_name='department',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='ترتيب ظهور القسم في القائمة', verbose_name='الترتيب'),
        ),
        migrations.AddField(
            model_name='department',
            name='show_in_menu',
            field=models.BooleanField(default=True, verbose_name='إظهار في القائمة'),
        ),
        migrations.AddField(
            model_name='field',
            name='css_class',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='فئة CSS'),
        ),
        migrations.AddField(
            model_name='field',
            name='default_value',
            field=models.TextField(blank=True, null=True, verbose_name='القيمة الافتراضية'),
        ),
        migrations.AddField(
            model_name='field',
            name='description',
            field=models.TextField(blank=True, help_text='وصف توضيحي للحقل يظهر للمستخدم', null=True, verbose_name='وصف الحقل'),
        ),
        migrations.AddField(
            model_name='field',
            name='help_text',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='نص المساعدة'),
        ),
        migrations.AddField(
            model_name='field',
            name='is_filterable',
            field=models.BooleanField(default=True, verbose_name='قابل للتصفية'),
        ),
        migrations.AddField(
            model_name='field',
            name='is_hidden',
            field=models.BooleanField(default=False, verbose_name='مخفي'),
        ),
        migrations.AddField(
            model_name='field',
            name='is_readonly',
            field=models.BooleanField(default=False, verbose_name='للقراءة فقط'),
        ),
        migrations.AddField(
            model_name='field',
            name='is_searchable',
            field=models.BooleanField(default=True, verbose_name='قابل للبحث'),
        ),
        migrations.AddField(
            model_name='field',
            name='is_unique',
            field=models.BooleanField(default=False, verbose_name='قيمة فريدة'),
        ),
        migrations.AddField(
            model_name='field',
            name='placeholder',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='نص توضيحي'),
        ),
        migrations.AddField(
            model_name='field',
            name='show_in_detail',
            field=models.BooleanField(default=True, verbose_name='عرض في التفاصيل'),
        ),
        migrations.AddField(
            model_name='field',
            name='show_in_export',
            field=models.BooleanField(default=True, verbose_name='عرض في التصدير'),
        ),
        migrations.AddField(
            model_name='field',
            name='show_in_table',
            field=models.BooleanField(default=True, verbose_name='عرض في الجدول'),
        ),
        migrations.AddField(
            model_name='field',
            name='validation_params',
            field=models.JSONField(blank=True, null=True, verbose_name='معلمات التحقق'),
        ),
        migrations.AddField(
            model_name='field',
            name='validation_type',
            field=models.CharField(choices=[('none', 'بدون تحقق'), ('regex', 'تعبير منتظم'), ('min_length', 'الحد الأدنى للطول'), ('max_length', 'الحد الأقصى للطول'), ('min_value', 'الحد الأدنى للقيمة'), ('max_value', 'الحد الأقصى للقيمة'), ('email', 'بريد إلكتروني'), ('url', 'رابط'), ('phone', 'رقم هاتف'), ('custom', 'تحقق مخصص')], default='none', max_length=20, verbose_name='نوع التحقق'),
        ),
        migrations.AddField(
            model_name='field',
            name='width',
            field=models.CharField(blank=True, help_text='مثال: 100%, 200px', max_length=50, null=True, verbose_name='العرض'),
        ),
        migrations.AlterField(
            model_name='field',
            name='type',
            field=models.CharField(choices=[('text', 'نص قصير'), ('textarea', 'نص طويل'), ('rich_text', 'نص منسق'), ('email', 'بريد إلكتروني'), ('url', 'رابط'), ('phone', 'رقم هاتف'), ('number', 'رقم'), ('decimal', 'رقم عشري'), ('currency', 'عملة'), ('percentage', 'نسبة مئوية'), ('date', 'تاريخ'), ('time', 'وقت'), ('datetime', 'تاريخ ووقت'), ('select', 'قائمة منسدلة'), ('multi_select', 'اختيار متعدد'), ('radio', 'اختيار واحد'), ('checkbox', 'صندوق اختيار'), ('file', 'ملف'), ('image', 'صورة'), ('pdf', 'ملف PDF'), ('signature', 'توقيع إلكتروني'), ('location', 'موقع جغرافي'), ('user', 'مستخدم'), ('department', 'قسم'), ('color', 'لون')], max_length=20, verbose_name='نوع الحقل'),
        ),
        migrations.AlterUniqueTogether(
            name='field',
            unique_together={('department', 'name')},
        ),
        migrations.CreateModel(
            name='DepartmentData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='الرقم المرجعي')),
                ('data', models.JSONField(verbose_name='البيانات')),
                ('files', models.JSONField(blank=True, null=True, verbose_name='الملفات')),
                ('status', models.CharField(default='active', max_length=50, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_department_data', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='data_entries', to='departments.department', verbose_name='القسم')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_department_data', to=settings.AUTH_USER_MODEL, verbose_name='تم التحديث بواسطة')),
            ],
            options={
                'verbose_name': 'بيانات القسم',
                'verbose_name_plural': 'بيانات الأقسام',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DepartmentDataHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data_snapshot', models.JSONField(verbose_name='نسخة البيانات')),
                ('action', models.CharField(max_length=50, verbose_name='الإجراء')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='التوقيت')),
                ('data_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='departments.departmentdata', verbose_name='بيانات القسم')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل تاريخ البيانات',
                'verbose_name_plural': 'سجلات تاريخ البيانات',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='FieldGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف المجموعة')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_collapsed', models.BooleanField(default=False, verbose_name='مطوية افتراضيًا')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='field_groups', to='departments.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'مجموعة حقول',
                'verbose_name_plural': 'مجموعات الحقول',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='field',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='fields', to='departments.fieldgroup', verbose_name='المجموعة'),
        ),
    ]
