{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}رفع مستند للكفالة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>رفع مستند للكفالة</h1>
    <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى تفاصيل الكفالة
    </a>
</div>

<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">رفع مستند للكفالة للمستفيد "{{ guarantee.beneficiary_name }}"</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.file.id_for_label }}">{{ form.file.label }}</label>
                {{ form.file }}
                {% if form.file.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.file.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">الملفات المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</small>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-upload"></i> رفع المستند
                </button>
                <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
