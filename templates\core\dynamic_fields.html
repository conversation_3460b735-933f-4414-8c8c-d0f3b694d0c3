{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}إدارة الحقول المخصصة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">إدارة الحقول المخصصة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <a href="{% url 'core:create_dynamic_field' %}" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة حقل جديد
                        </a>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الحقل</th>
                                    <th>الاسم البرمجي</th>
                                    <th>القسم</th>
                                    <th>نوع الحقل</th>
                                    <th>مطلوب</th>
                                    <th>الترتيب</th>
                                    <th>نشط</th>
                                    <th>افتراضي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in fields %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ field.display_name }}</td>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.get_section_display }}</td>
                                    <td>{{ field.get_field_type_display }}</td>
                                    <td>
                                        {% if field.required %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ field.order }}</td>
                                    <td>
                                        {% if field.is_active %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-danger">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.is_default %}
                                        <span class="badge bg-info">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'core:update_dynamic_field' field.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        {% if not field.is_default %}
                                        <a href="{% url 'core:delete_dynamic_field' field.id %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center">لا توجد حقول مخصصة حتى الآن.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
