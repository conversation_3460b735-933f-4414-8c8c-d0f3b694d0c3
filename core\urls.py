from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/<int:pk>/mark-as-read/', views.mark_notification_as_read, name='mark_notification_as_read'),
    path('settings/', views.settings, name='settings'),
    path('settings/update-ui/', views.update_ui, name='update_ui'),
    path('backups/', views.backups, name='backups'),
    path('backups/create/', views.create_backup, name='create_backup'),
    path('backups/<int:pk>/download/', views.download_backup, name='download_backup'),
    path('backups/<int:pk>/delete/', views.delete_backup, name='delete_backup'),


]
