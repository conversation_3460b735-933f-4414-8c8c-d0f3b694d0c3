from django.urls import path
from . import views

app_name = 'committees'

urlpatterns = [
    path('', views.committee_list, name='list'),
    path('create/', views.committee_create, name='create'),
    path('<int:pk>/', views.committee_detail, name='detail'),
    path('<int:pk>/update/', views.committee_update, name='update'),
    path('<int:pk>/delete/', views.committee_delete, name='delete'),
    path('<int:pk>/add-member/', views.add_member, name='add_member'),
    path('members/<int:pk>/delete/', views.delete_member, name='delete_member'),
    path('<int:pk>/extend/', views.extend_committee, name='extend'),
    path('<int:pk>/close/', views.close_committee, name='close'),
    path('<int:pk>/upload-document/', views.upload_document, name='upload_document'),
    path('export-excel/', views.export_committees_excel, name='export_excel'),
    path('import-excel/', views.import_committees_excel, name='import_excel'),
]
