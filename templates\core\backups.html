{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}النسخ الاحتياطية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>النسخ الاحتياطية</h1>
    <div>
        <a href="{% url 'core:settings' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى الإعدادات
        </a>
        <a href="{% url 'core:create_backup' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إنشاء نسخة احتياطية جديدة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">قائمة النسخ الاحتياطية</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تم الإنشاء بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for backup in backups %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ backup.name }}</td>
                        <td>{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                        <td>{{ backup.created_by.get_full_name|default:backup.created_by.username }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'core:download_backup' backup.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-download"></i> تنزيل
                                </a>
                                <a href="{% url 'core:delete_backup' backup.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center">لا توجد نسخ احتياطية مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
