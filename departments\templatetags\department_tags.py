from django import template

register = template.Library()

@register.filter(name='getattribute')
def getattribute(obj, attr):
    """
    فلتر مخصص للوصول إلى سمة كائن باستخدام اسم السمة كسلسلة نصية
    """
    try:
        # إذا كان attr يحتوي على نقطة، نقسمه ونتعامل معه كسلسلة من السمات
        if '.' in attr:
            parts = attr.split('.')
            result = obj
            for part in parts:
                if isinstance(result, dict):
                    result = result.get(part)
                else:
                    result = getattr(result, part)
            return result
        else:
            # التعامل العادي مع السمة الواحدة
            return obj[attr] if isinstance(obj, dict) else getattr(obj, attr)
    except (KeyError, AttributeError, TypeError):
        return None

@register.filter(name='split')
def split(value, delimiter):
    """
    فلتر مخصص لتقسيم سلسلة نصية باستخدام محدد معين
    مثال: {{ value|split:"/" }}
    """
    if value is None or value == '':
        return []
    return value.split(delimiter)

@register.filter(name='get_data_field')
def get_data_field(data_entry, field_name):
    """
    فلتر مخصص للحصول على قيمة حقل من كائن DepartmentData
    مثال: {{ data_entry|get_data_field:"field_name" }}
    """
    if hasattr(data_entry, 'get_data_value'):
        return data_entry.get_data_value(field_name)
    return None
