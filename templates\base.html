{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الدائرة القانونية{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.rtlcss.com/bootstrap/v4.5.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
    <!-- طبقة التراكب للأجهزة المحمولة -->
    <div class="sidebar-overlay"></div>

    <!-- القائمة الجانبية -->
    <aside class="sidebar">
        <!-- رأس القائمة الجانبية -->
        <div class="sidebar-header">
            <a href="{% url 'dashboard' %}" class="sidebar-brand">
                <i class="fas fa-balance-scale"></i>
                <span class="sidebar-brand-text">نظام إدارة الدائرة القانونية</span>
            </a>
            <button id="sidebar-toggle" class="sidebar-toggle" title="طي/فتح القائمة">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <!-- قائمة العناصر -->
        <ul class="sidebar-menu">
            <!-- لوحة التحكم -->
            <li class="sidebar-item">
                <a href="{% url 'dashboard' %}" class="sidebar-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
            </li>

            <!-- اللجان والمجالس -->
            <li class="sidebar-item">
                <a href="#" class="sidebar-link sidebar-dropdown-toggle" aria-expanded="false">
                    <i class="fas fa-gavel"></i>
                    <span class="sidebar-text">اللجان والمجالس</span>
                </a>
                <ul class="sidebar-dropdown">
                    <li><a href="{% url 'committees:list' %}" class="sidebar-dropdown-item">عرض اللجان</a></li>
                    <li><a href="{% url 'committees:create' %}" class="sidebar-dropdown-item">إضافة لجنة جديدة</a></li>
                </ul>
            </li>

            <!-- الكفالات والاستشارات -->
            <li class="sidebar-item">
                <a href="#" class="sidebar-link sidebar-dropdown-toggle" aria-expanded="false">
                    <i class="fas fa-shield-alt"></i>
                    <span class="sidebar-text">الكفالات والاستشارات</span>
                </a>
                <ul class="sidebar-dropdown">
                    <li><a href="{% url 'guarantees:list' %}" class="sidebar-dropdown-item">عرض الكفالات</a></li>
                    <li><a href="{% url 'guarantees:create' %}" class="sidebar-dropdown-item">إضافة كفالة جديدة</a></li>
                </ul>
            </li>

            <!-- التقارير -->
            <li class="sidebar-item">
                <a href="#" class="sidebar-link sidebar-dropdown-toggle" aria-expanded="false">
                    <i class="fas fa-chart-bar"></i>
                    <span class="sidebar-text">التقارير</span>
                </a>
                <ul class="sidebar-dropdown">
                    <li><a href="{% url 'reports:committees' %}" class="sidebar-dropdown-item">تقارير اللجان</a></li>
                    <li><a href="{% url 'reports:guarantees' %}" class="sidebar-dropdown-item">تقارير الكفالات</a></li>
                </ul>
            </li>



            <!-- قائمة الإدارة للمشرفين -->
            {% if user.user_type == 'admin' %}
            <li class="sidebar-item">
                <a href="#" class="sidebar-link sidebar-dropdown-toggle" aria-expanded="false">
                    <i class="fas fa-cogs"></i>
                    <span class="sidebar-text">الإدارة</span>
                </a>
                <ul class="sidebar-dropdown">
                    <li><a href="{% url 'accounts:user_list' %}" class="sidebar-dropdown-item">إدارة المستخدمين</a></li>
                    <li><a href="{% url 'core:backups' %}" class="sidebar-dropdown-item">النسخ الاحتياطية</a></li>
                    <li><a href="{% url 'core:settings' %}" class="sidebar-dropdown-item">الإعدادات</a></li>
                </ul>
            </li>
            {% endif %}
        </ul>

        <!-- قسم المستخدم -->
        <div class="sidebar-user">
            <div class="user-info">
                <div class="user-avatar">
                    {{ user.get_initials }}
                </div>
                <div class="user-details">
                    <p class="user-name">{{ user.get_full_name|default:user.username }}</p>
                    <p class="user-role">{{ user.get_user_type_display }}</p>
                </div>
            </div>
            <div class="user-actions">
                <a href="{% url 'accounts:profile' %}" class="user-action" title="الملف الشخصي">
                    <i class="fas fa-id-card"></i>
                </a>
                <a href="{% url 'core:notifications' %}" class="user-action position-relative" title="الإشعارات">
                    <i class="fas fa-bell"></i>
                    {% if unread_notifications_count > 0 %}
                    <span class="notification-badge">{{ unread_notifications_count }}</span>
                    {% endif %}
                </a>
                <a href="{% url 'logout' %}" class="user-action" title="تسجيل الخروج">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </div>
    </aside>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- رأس الصفحة -->
        <header class="main-header">
            <button id="mobile-menu-toggle" class="mobile-menu-toggle d-lg-none">
                <i class="fas fa-bars"></i>
            </button>
            <h4 class="header-title">{% block header_title %}لوحة التحكم{% endblock %}</h4>
            <div class="header-actions">
                <a href="{% url 'core:notifications' %}" class="header-action position-relative" title="الإشعارات">
                    <i class="fas fa-bell"></i>
                    {% if unread_notifications_count > 0 %}
                    <span class="badge badge-danger badge-notification">{{ unread_notifications_count }}</span>
                    {% endif %}
                </a>
            </div>
        </header>

        <!-- محتوى الصفحة -->
        <div class="content-wrapper">
            {% if messages %}
            <div class="messages">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}{% endblock %}
        </div>

        <!-- تذييل الصفحة -->
        <footer class="main-footer">
            <span class="text-muted">جميع الحقوق محفوظة &copy; {% now "Y" %} نظام إدارة الدائرة القانونية</span>
        </footer>
    </div>
    {% else %}
    <div class="container mt-4">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block auth_content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">جميع الحقوق محفوظة &copy; {% now "Y" %} نظام إدارة الدائرة القانونية</span>
        </div>
    </footer>
    {% endif %}

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.rtlcss.com/bootstrap/v4.5.3/js/bootstrap.min.js"></script>
    <script src="{% static 'js/script.js' %}"></script>
    {% if user.is_authenticated %}
    <script src="{% static 'js/sidebar.js' %}"></script>
    {% endif %}
    {% block extra_js %}{% endblock %}
</body>
</html>
