{% extends 'base.html' %}
{% load department_tags %}

{% block title %}{{ data_entry.title }} - {{ department.name }}{% endblock %}

{% block extra_css %}
<style>
    .field-group {
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .field-group-header {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
    }
    .field-group-body {
        padding: 15px;
    }
    .field-row {
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #eee;
    }
    .field-label {
        font-weight: bold;
        color: #555;
    }
    .field-value {
        word-break: break-word;
    }
    .field-value img {
        max-width: 100%;
        height: auto;
    }
    .history-timeline {
        position: relative;
        padding-left: 30px;
    }
    .history-timeline::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 10px;
        width: 2px;
        background-color: #ddd;
    }
    .history-item {
        position: relative;
        margin-bottom: 20px;
    }
    .history-item::before {
        content: '';
        position: absolute;
        top: 5px;
        left: -30px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #007bff;
    }
    .history-item.create::before {
        background-color: #28a745;
    }
    .history-item.update::before {
        background-color: #ffc107;
    }
    .history-item.delete::before {
        background-color: #dc3545;
    }
    .history-item.import::before {
        background-color: #17a2b8;
    }
    .history-time {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .history-user {
        font-weight: bold;
    }
    .history-action {
        display: inline-block;
        padding: 2px 5px;
        border-radius: 3px;
        font-size: 0.8rem;
        color: white;
    }
    .history-action.create {
        background-color: #28a745;
    }
    .history-action.update {
        background-color: #ffc107;
        color: #212529;
    }
    .history-action.delete {
        background-color: #dc3545;
    }
    .history-action.import {
        background-color: #17a2b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>بيانات الإدخال</h1>
    <div>
        <a href="{% url 'departments:data_list' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة البيانات
        </a>
        <a href="{% url 'departments:data_update' data_entry.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'departments:data_delete' data_entry.id %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- تم إخفاء معلومات البيانات الأساسية بناءً على طلب المستخدم -->

        <!-- بيانات الحقول -->
        {% regroup fields by group as grouped_fields %}

        <!-- الحقول المجمعة -->
        {% for group in grouped_fields %}
            {% if group.grouper %}
            <div class="field-group mb-4">
                <div class="field-group-header">
                    {{ group.grouper.name }}
                </div>
                <div class="field-group-body">
                    {% for field in group.list %}
                    <div class="field-row">
                        <div class="field-label">{{ field.display_name }}:</div>
                        <div class="field-value">
                            {% with value=data_entry|get_data_field:field.name|default:"-" %}
                                {% if field.type == 'file' or field.type == 'image' or field.type == 'pdf' %}
                                    {% if value %}
                                    <a href="{{ value }}" target="_blank">{{ value|split:"/"|last }}</a>
                                    {% else %}
                                    -
                                    {% endif %}
                                {% elif field.type == 'image' %}
                                    {% if value %}
                                    <img src="{{ value }}" alt="{{ field.display_name }}" class="img-thumbnail">
                                    {% else %}
                                    -
                                    {% endif %}
                                {% elif field.type == 'date' %}
                                    {{ value|date:"Y-m-d"|default:"-" }}
                                {% elif field.type == 'datetime' %}
                                    {{ value|date:"Y-m-d H:i"|default:"-" }}
                                {% elif field.type == 'checkbox' %}
                                    {% if value %}
                                    <i class="fas fa-check-square text-success"></i>
                                    {% else %}
                                    <i class="fas fa-square text-muted"></i>
                                    {% endif %}
                                {% elif field.type == 'rich_text' %}
                                    {{ value|safe|default:"-" }}
                                {% else %}
                                    {{ value|default:"-" }}
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        {% endfor %}

        <!-- الحقول غير المجمعة -->
        <div class="field-group mb-4">
            <div class="field-group-header">
                بيانات الإدخال
            </div>
            <div class="field-group-body">
                {% for field in fields %}
                    {% if not field.group %}
                    <div class="field-row">
                        <div class="field-label">{{ field.display_name }}:</div>
                        <div class="field-value">
                            {% with value=data_entry|get_data_field:field.name|default:"-" %}
                                {% if field.type == 'file' or field.type == 'image' or field.type == 'pdf' %}
                                    {% if value %}
                                    <a href="{{ value }}" target="_blank">{{ value|split:"/"|last }}</a>
                                    {% else %}
                                    -
                                    {% endif %}
                                {% elif field.type == 'image' %}
                                    {% if value %}
                                    <img src="{{ value }}" alt="{{ field.display_name }}" class="img-thumbnail">
                                    {% else %}
                                    -
                                    {% endif %}
                                {% elif field.type == 'date' %}
                                    {{ value|date:"Y-m-d"|default:"-" }}
                                {% elif field.type == 'datetime' %}
                                    {{ value|date:"Y-m-d H:i"|default:"-" }}
                                {% elif field.type == 'checkbox' %}
                                    {% if value %}
                                    <i class="fas fa-check-square text-success"></i>
                                    {% else %}
                                    <i class="fas fa-square text-muted"></i>
                                    {% endif %}
                                {% elif field.type == 'rich_text' %}
                                    {{ value|safe|default:"-" }}
                                {% else %}
                                    {{ value|default:"-" }}
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- سجل التاريخ -->
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">سجل التاريخ</h5>
            </div>
            <div class="card-body">
                {% if history %}
                <div class="history-timeline">
                    {% for entry in history %}
                    <div class="history-item {{ entry.action }}">
                        <div class="history-time">{{ entry.timestamp|date:"Y-m-d H:i" }}</div>
                        <div class="history-user">{{ entry.user.get_full_name|default:entry.user.username }}</div>
                        <div>
                            <span class="history-action {{ entry.action }}">
                                {% if entry.action == 'create' %}
                                إنشاء
                                {% elif entry.action == 'update' %}
                                تحديث
                                {% elif entry.action == 'delete' %}
                                حذف
                                {% elif entry.action == 'import' %}
                                استيراد
                                {% else %}
                                {{ entry.action }}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا يوجد سجل تاريخ متاح.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
