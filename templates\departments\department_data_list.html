{% extends 'base.html' %}
{% load department_tags %}

{% block title %}{{ department.display_name|default:department.table_name|default:"بيانات الإدخال" }} - {{ department.name }}{% endblock %}

{% block extra_css %}
<style>
    .search-form {
        margin-bottom: 20px;
    }
    .export-buttons {
        margin-bottom: 20px;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .status-active {
        color: #28a745;
    }
    .status-inactive {
        color: #dc3545;
    }
    .status-pending {
        color: #ffc107;
    }
    /* تحسين عرض الجدول */
    #dataTable {
        width: 100%;
        table-layout: auto;
    }
    #dataTable th, #dataTable td {
        padding: 8px;
        vertical-align: middle;
        white-space: nowrap;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    /* تنسيق الأعمدة المخفية */
    #dataTable .d-none {
        display: none !important;
    }
    #dataTable td.expandable {
        white-space: normal;
        word-break: break-word;
        max-width: none;
        min-width: 200px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    /* تنسيق الصور */
    #dataTable img.img-thumbnail {
        max-width: 50px;
        max-height: 50px;
    }
    /* تنسيق الروابط */
    #dataTable a {
        color: #007bff;
        text-decoration: none;
    }
    #dataTable a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ department.display_name|default:department.table_name|default:"بيانات الإدخال" }} - {{ department.name }}</h1>
    <div>
        <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل القسم
        </a>
        <a href="{% url 'departments:data_create' department.id %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة بيانات إدخال جديدة
        </a>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">البحث والتصفية في {{ department.display_name|default:department.table_name|default:"بيانات الإدخال" }}</h5>
        <button class="btn btn-sm btn-light" type="button" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
            <i class="fas fa-filter"></i> خيارات البحث المتقدم
        </button>
    </div>
    <div class="card-body">
        <form method="get" class="search-form">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="search">بحث:</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                            <input type="text" name="search" id="search" class="form-control" value="{{ search_query }}" placeholder="ابحث في العنوان، الرقم المرجعي، البيانات...">
                        </div>
                        <small class="form-text text-muted">البحث في العنوان، الرقم المرجعي، والبيانات</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="status">الحالة:</label>
                        <select name="status" id="status" class="form-control">
                            <option value="">-- جميع الحالات --</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                            <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="sort">ترتيب حسب:</label>
                        <select name="sort" id="sort" class="form-control">
                            <option value="-created_at" {% if sort_by == '-created_at' %}selected{% endif %}>الأحدث أولاً</option>
                            <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>الأقدم أولاً</option>
                            <option value="title" {% if sort_by == 'title' %}selected{% endif %}>العنوان (أ-ي)</option>
                            <option value="-title" {% if sort_by == '-title' %}selected{% endif %}>العنوان (ي-أ)</option>
                            <option value="id" {% if sort_by == 'id' %}selected{% endif %}>رقم الإدخال (تصاعدي)</option>
                            <option value="-id" {% if sort_by == '-id' %}selected{% endif %}>رقم الإدخال (تنازلي)</option>
                            <option value="status" {% if sort_by == 'status' %}selected{% endif %}>الحالة</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- خيارات البحث المتقدم -->
            <div class="collapse mt-3" id="filterCollapse">
                <div class="card card-body bg-light">
                    <h6 class="mb-3">خيارات البحث المتقدم</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_from">من تاريخ:</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_to">إلى تاريخ:</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                            </div>
                        </div>
                    </div>

                    {% if users %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="user">المستخدم:</label>
                                <select name="user" id="user" class="form-control">
                                    <option value="">-- جميع المستخدمين --</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}" {% if user_filter == user.id|stringformat:"i" %}selected{% endif %}>
                                        {{ user.get_full_name|default:user.username }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="items_per_page">عدد العناصر في الصفحة:</label>
                                <select name="items_per_page" id="items_per_page" class="form-control">
                                    <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                    <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if filterable_fields %}
                    <h6 class="mt-3 mb-2">تصفية حسب الحقول المخصصة:</h6>
                    <div class="row">
                        {% for field in filterable_fields %}
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="field_{{ field.id }}">{{ field.display_name }}:</label>
                                <input type="text" name="field_{{ field.id }}" id="field_{{ field.id }}" class="form-control" value="{{ request.GET.field_field.id }}">
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> بحث
                </button>
                <a href="{% url 'departments:data_list' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- أزرار التصدير -->
<div class="export-buttons d-flex justify-content-between">
    <div>
        <div class="btn-group">
            <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fas fa-file-export"></i> تصدير البيانات
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="{% url 'departments:data_export' department.id %}?export_type=csv{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                    <i class="fas fa-file-csv"></i> تصدير CSV
                </a>
                <a class="dropdown-item" href="{% url 'departments:data_export' department.id %}?export_type=excel{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </a>
                <a class="dropdown-item" href="{% url 'departments:data_export' department.id %}?export_type=json{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">
                    <i class="fas fa-file-code"></i> تصدير JSON
                </a>
            </div>
        </div>
        <a href="{% url 'departments:data_import' department.id %}" class="btn btn-warning">
            <i class="fas fa-file-import"></i> استيراد بيانات الإدخال
        </a>
    </div>
    <div>
        <a href="{% url 'departments:data_report' department.id %}" class="btn btn-info">
            <i class="fas fa-chart-bar"></i> التقارير والإحصائيات
        </a>
    </div>
</div>

<!-- جدول البيانات -->
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ department.display_name|default:department.table_name|default:"بيانات الإدخال" }}</h5>
        <div>
            <div class="dropdown">
                <button class="btn btn-sm btn-light dropdown-toggle" id="toggleColumns" title="إظهار/إخفاء الأعمدة" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة
                    <span class="badge badge-info column-count"></span>
                </button>
                <div class="dropdown-menu dropdown-menu-right p-3" id="columnsDropdown" style="width: 300px; max-height: 500px; overflow-y: auto;">
                    <h6 class="dropdown-header">إظهار/إخفاء الأعمدة</h6>
                    <div id="columnsContainer"></div>
                    <div class="dropdown-divider"></div>
                    <button class="btn btn-sm btn-secondary btn-block" id="resetColumns">إعادة تعيين</button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="dataTable">
                <thead class="thead-dark">
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>الرقم المرجعي</th>
                        {% for field in table_fields %}
                        <th>{{ field.display_name }}</th>
                        {% endfor %}
                        <th>تاريخ الإنشاء</th>
                        <th>آخر تحديث</th>
                        <th>الحالة</th>
                        <th>بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in page_obj %}
                    <tr>
                        <td>{{ forloop.counter0|add:page_obj.start_index }}</td>
                        <td>
                            <span class="text-primary font-weight-bold">{{ entry.title }}</span>
                        </td>
                        <td>{{ entry.reference_number|default:"-" }}</td>
                        {% for field in table_fields %}
                        <td>
                            {% if entry.data and field.name in entry.data %}
                                {% with value=entry.data|getattribute:field.name|default:"-" %}
                                    {% if field.type == 'file' or field.type == 'image' or field.type == 'pdf' %}
                                        {% if value %}
                                        <a href="{{ value }}" target="_blank">{{ value|split:"/"|last }}</a>
                                        {% else %}
                                        -
                                        {% endif %}
                                    {% elif field.type == 'image' %}
                                        {% if value %}
                                        <img src="{{ value }}" alt="{{ field.display_name }}" class="img-thumbnail" style="max-width: 50px;">
                                        {% else %}
                                        -
                                        {% endif %}
                                    {% elif field.type == 'date' %}
                                        {% if value %}
                                        {{ value|date:"Y-m-d" }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    {% elif field.type == 'datetime' %}
                                        {% if value %}
                                        {{ value|date:"Y-m-d H:i" }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    {% elif field.type == 'checkbox' %}
                                        {% if value %}
                                        <i class="fas fa-check text-success"></i>
                                        {% else %}
                                        <i class="fas fa-times text-danger"></i>
                                        {% endif %}
                                    {% else %}
                                        {{ value }}
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        {% endfor %}
                        <td>{{ entry.created_at|date:"Y-m-d H:i" }}</td>
                        <td>{{ entry.updated_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            {% if entry.status == 'active' %}
                            <span class="badge badge-success"><i class="fas fa-check-circle"></i> نشط</span>
                            {% elif entry.status == 'inactive' %}
                            <span class="badge badge-danger"><i class="fas fa-times-circle"></i> غير نشط</span>
                            {% elif entry.status == 'pending' %}
                            <span class="badge badge-warning"><i class="fas fa-clock"></i> قيد الانتظار</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ entry.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if entry.created_by %}
                            {{ entry.created_by.get_full_name|default:entry.created_by.username }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'departments:data_detail' entry.id %}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'departments:data_update' entry.id %}" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'departments:data_delete' entry.id %}" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item" href="{% url 'departments:data_detail' entry.id %}">
                                        <i class="fas fa-eye"></i> عرض التفاصيل
                                    </a>
                                    <a class="dropdown-item" href="{% url 'departments:data_update' entry.id %}">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item text-danger" href="{% url 'departments:data_delete' entry.id %}">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- التنقل بين الصفحات -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}

                {% for i in page_obj.paginator.page_range %}
                    {% if page_obj.number == i %}
                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ i }}</a></li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد بيانات متاحة.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // التأكد من تحميل jQuery
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded!');
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }

    $(document).ready(function() {
        // تحديث الصفحة عند تغيير الترتيب
        $('#sort').change(function() {
            $(this).closest('form').submit();
        });

        // تحديث الصفحة عند تغيير الحالة
        $('#status').change(function() {
            $(this).closest('form').submit();
        });

        // إضافة وظيفة إظهار وإخفاء الأعمدة
        console.log('Script loaded and ready');

        // إنشاء خيارات الأعمدة عند تحميل الصفحة
        function populateColumnsDropdown() {
            console.log('Populating columns dropdown');
            // مسح المحتوى السابق
            $('#columnsContainer').empty();

            // إضافة خيارات للأعمدة
            $('#dataTable thead th').each(function(index) {
                var columnName = $(this).text().trim();
                if (columnName === '') {
                    columnName = 'عمود ' + (index + 1);
                }

                // استخدام nth-child بدلاً من eq للتوافق مع CSS
                var isVisible = !$(this).hasClass('d-none');
                var columnIndex = index + 1;

                var checkboxItem = $('<div class="form-check"></div>');
                var checkbox = $('<input class="form-check-input column-toggle" type="checkbox" id="column' + index + '" data-column="' + columnIndex + '" ' + (isVisible ? 'checked' : '') + '>');
                var label = $('<label class="form-check-label" for="column' + index + '">' + columnName + '</label>');

                checkboxItem.append(checkbox).append(label);
                $('#columnsContainer').append(checkboxItem);
            });

            console.log('Columns dropdown populated');
        }

        // تحميل خيارات الأعمدة عند فتح القائمة المنسدلة
        $('#toggleColumns').on('click', function() {
            console.log('Toggle columns button clicked');
            populateColumnsDropdown();
        });

        // تبديل عرض الأعمدة عند النقر على خانات الاختيار
        $(document).on('change', '.column-toggle', function() {
            console.log('Column toggle changed');
            var columnIndex = $(this).data('column');
            var isVisible = $(this).prop('checked');

            console.log('Toggling column', columnIndex, 'visibility to', isVisible);

            // استخدام nth-child بدلاً من eq للتوافق مع CSS
            $('#dataTable th:nth-child(' + columnIndex + ')').toggleClass('d-none', !isVisible);
            $('#dataTable td:nth-child(' + columnIndex + ')').toggleClass('d-none', !isVisible);

            // حفظ حالة الأعمدة في التخزين المحلي
            saveColumnState();

            // تحديث عدد الأعمدة المرئية
            updateVisibleColumnsCount();
        });

        // إعادة تعيين جميع الأعمدة
        $(document).on('click', '#resetColumns', function() {
            console.log('Reset columns button clicked');
            $('#dataTable th, #dataTable td').removeClass('d-none');
            $('.column-toggle').prop('checked', true);

            // حفظ حالة الأعمدة في التخزين المحلي
            saveColumnState();

            // تحديث عدد الأعمدة المرئية
            updateVisibleColumnsCount();
        });

        // وظيفة لحفظ حالة الأعمدة في التخزين المحلي
        function saveColumnState() {
            var columnState = [];

            $('.column-toggle').each(function() {
                columnState.push({
                    column: $(this).data('column'),
                    visible: $(this).prop('checked')
                });
            });

            try {
                // استخدام معرف القسم في اسم المفتاح للتخزين المحلي
                var departmentId = {{ department.id }};
                localStorage.setItem('departmentDataColumns_' + departmentId, JSON.stringify(columnState));
            } catch (e) {
                console.error('Error saving column state:', e);
            }
        }

        // وظيفة لاستعادة حالة الأعمدة من التخزين المحلي
        function restoreColumnState() {
            try {
                var departmentId = {{ department.id }};
                var columnState = localStorage.getItem('departmentDataColumns_' + departmentId);

                if (columnState) {
                    columnState = JSON.parse(columnState);

                    columnState.forEach(function(state) {
                        if (!state.visible) {
                            // استخدام nth-child بدلاً من eq للتوافق مع CSS
                            $('#dataTable th:nth-child(' + state.column + ')').addClass('d-none');
                            $('#dataTable td:nth-child(' + state.column + ')').addClass('d-none');
                        }
                    });
                }
            } catch (e) {
                console.error('Error restoring column state:', e);
                // في حالة حدوث خطأ، نحذف حالة الأعمدة المخزنة
                localStorage.removeItem('departmentDataColumns_' + {{ department.id }});
            }
        }

        // تأخير استعادة حالة الأعمدة لضمان تحميل الجدول بالكامل
        setTimeout(function() {
            // استعادة حالة الأعمدة عند تحميل الصفحة
            restoreColumnState();

            // تحديث عدد الأعمدة المرئية
            updateVisibleColumnsCount();

            console.log('Column state restored and count updated');
        }, 500);

        // وظيفة لتحديث عدد الأعمدة المرئية
        function updateVisibleColumnsCount() {
            try {
                var totalColumns = $('#dataTable thead th').length;
                var hiddenColumns = $('#dataTable thead th.d-none').length;
                var visibleColumns = totalColumns - hiddenColumns;
                $('.column-count').text(visibleColumns + '/' + totalColumns);
                console.log('Column count updated:', visibleColumns, '/', totalColumns);
            } catch (e) {
                console.error('Error updating column count:', e);
            }
        }

        // تحديث عدد الأعمدة المرئية عند تحميل الصفحة
        updateVisibleColumnsCount();

        // تفعيل البحث في الجدول
        $('#search').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('#dataTable tbody tr').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });

        // إضافة وظيفة لتوسيع الخلايا عند النقر عليها
        $(document).on('click', '#dataTable td', function(e) {
            // تجاهل النقر على الروابط والأزرار داخل الخلايا
            if ($(e.target).is('a, button, input, .btn, i')) {
                return;
            }
            $(this).toggleClass('expandable');
        });
    });
</script>
{% endblock %}
