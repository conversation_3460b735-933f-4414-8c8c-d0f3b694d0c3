#!/usr/bin/env python3
"""
سكريبت لإنشاء ملف Excel تجريبي لاختبار استيراد اللجان
"""

from openpyxl import Workbook
from datetime import datetime, timedelta

def create_test_excel():
    """إنشاء ملف Excel تجريبي"""
    
    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "اللجان والمجالس"
    
    # تعيين العناوين
    headers = [
        'العنوان', 'رقم الأمر الإداري', 'تاريخ التشكيل', 'النوع', 'الجهة المسؤولة',
        'المدة (بالأيام)', 'تاريخ الانتهاء', 'الحالة', 'رتبة رئيس اللجنة', 'اسم رئيس اللجنة',
        'جهة إصدار الأمر', 'رقم اضبارة الحفظ', 'تقدم الإجراءات', 'نوع المخالفة',
        'رتبة المتهم', 'مصير المجلس', 'محل التحقيق', 'آخر الإجراءات', 'ملاحظات'
    ]
    
    # إضافة العناوين
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # إضافة بيانات تجريبية
    test_data = [
        [
            'لجنة تحقيق تجريبية 1',  # العنوان
            'أ.إ/123/2025',  # رقم الأمر الإداري
            '2025-01-01',  # تاريخ التشكيل
            'تحقيقية',  # النوع
            'الإدارة القانونية',  # الجهة المسؤولة
            30,  # المدة بالأيام
            '2025-01-31',  # تاريخ الانتهاء
            'نشطة',  # الحالة
            'عقيد',  # رتبة رئيس اللجنة
            'أحمد محمد علي',  # اسم رئيس اللجنة
            'قيادة الشرطة',  # جهة إصدار الأمر
            12345,  # رقم اضبارة الحفظ
            'جاري التحقيق',  # تقدم الإجراءات
            'مخالفة إدارية',  # نوع المخالفة
            'نقيب',  # رتبة المتهم
            'قيد النظر',  # مصير المجلس
            'مقر القيادة',  # محل التحقيق
            'استدعاء الشهود',  # آخر الإجراءات
            'لا توجد ملاحظات'  # ملاحظات
        ],
        [
            'لجنة تحقيق تجريبية 2',
            'أ.إ/124/2025',
            '2025-01-02',
            'استشارية',
            'الإدارة المالية',
            45,
            '2025-02-16',
            'نشطة',
            'مقدم',
            'فاطمة أحمد محمد',
            'قيادة الشرطة',
            12346,
            'مرحلة أولية',
            'مخالفة مالية',
            'ملازم',
            'قيد النظر',
            'مقر الإدارة',
            'جمع الأدلة',
            'تحتاج متابعة'
        ]
    ]
    
    # إضافة البيانات التجريبية
    for row_idx, row_data in enumerate(test_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    # تعديل عرض الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 30)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # حفظ الملف
    filename = 'test_committees.xlsx'
    wb.save(filename)
    print(f"تم إنشاء ملف Excel تجريبي: {filename}")
    return filename

if __name__ == "__main__":
    create_test_excel()
