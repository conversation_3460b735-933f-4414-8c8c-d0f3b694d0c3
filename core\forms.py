from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Dynamic<PERSON>ield, DynamicData, Setting
import json

class DynamicFieldForm(forms.ModelForm):
    class Meta:
        model = DynamicField
        fields = ['section', 'name', 'display_name', 'field_type', 'required', 'options', 'order', 'is_active']
        widgets = {
            'section': forms.Select(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'display_name': forms.TextInput(attrs={'class': 'form-control'}),
            'field_type': forms.Select(attrs={'class': 'form-control'}),
            'required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'options': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_options(self):
        options = self.cleaned_data.get('options')
        field_type = self.cleaned_data.get('field_type')
        
        if field_type == 'select' and options:
            try:
                options_dict = json.loads(options)
                if not isinstance(options_dict, dict):
                    raise forms.ValidationError(_('الخيارات يجب أن تكون بتنسيق JSON صحيح (قاموس).'))
            except json.JSONDecodeError:
                raise forms.ValidationError(_('الخيارات يجب أن تكون بتنسيق JSON صحيح.'))
        
        return options
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if ' ' in name:
            raise forms.ValidationError(_('اسم الحقل البرمجي لا يمكن أن يحتوي على مسافات.'))
        return name

class DynamicDataForm(forms.Form):
    def __init__(self, *args, **kwargs):
        section = kwargs.pop('section')
        instance = kwargs.pop('instance', None)
        super(DynamicDataForm, self).__init__(*args, **kwargs)
        
        # Get all active dynamic fields for this section
        fields = DynamicField.objects.filter(section=section, is_active=True).order_by('order')
        
        for field in fields:
            field_name = f"dynamic_{field.id}"
            
            # Get initial value if instance is provided
            initial_value = None
            if instance:
                try:
                    dynamic_data = DynamicData.objects.get(
                        field=field,
                        content_type__model=instance._meta.model_name,
                        object_id=instance.id
                    )
                    initial_value = dynamic_data.get_value()
                except DynamicData.DoesNotExist:
                    pass
            
            # Create form field based on field type
            if field.field_type == 'text':
                self.fields[field_name] = forms.CharField(
                    label=field.display_name,
                    required=field.required,
                    initial=initial_value,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'number':
                self.fields[field_name] = forms.DecimalField(
                    label=field.display_name,
                    required=field.required,
                    initial=initial_value,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'date':
                self.fields[field_name] = forms.DateField(
                    label=field.display_name,
                    required=field.required,
                    initial=initial_value,
                    widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
                )
            elif field.field_type == 'select':
                choices = [('', '---------')]
                options = field.get_options_dict()
                for key, value in options.items():
                    choices.append((key, value))
                
                self.fields[field_name] = forms.ChoiceField(
                    label=field.display_name,
                    required=field.required,
                    initial=initial_value,
                    choices=choices,
                    widget=forms.Select(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'textarea':
                self.fields[field_name] = forms.CharField(
                    label=field.display_name,
                    required=field.required,
                    initial=initial_value,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
                )
            elif field.field_type == 'file':
                self.fields[field_name] = forms.FileField(
                    label=field.display_name,
                    required=field.required,
                    widget=forms.FileInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'boolean':
                self.fields[field_name] = forms.BooleanField(
                    label=field.display_name,
                    required=False,
                    initial=initial_value,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )

class SettingsForm(forms.ModelForm):
    class Meta:
        model = Setting
        fields = ['value']
        widgets = {
            'value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
