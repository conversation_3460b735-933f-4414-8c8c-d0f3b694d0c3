# Generated by Django 4.2.20 on 2025-06-23 05:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('committees', '0002_committee_responsible_entity'),
    ]

    operations = [
        migrations.AddField(
            model_name='committee',
            name='accused_rank',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الرتبة/العنوان الوظيفي للمتهم'),
        ),
        migrations.AddField(
            model_name='committee',
            name='chairman_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم رئيس اللجنة'),
        ),
        migrations.AddField(
            model_name='committee',
            name='chairman_rank',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الرتبة/العنوان الوظيفي'),
        ),
        migrations.AddField(
            model_name='committee',
            name='council_fate',
            field=models.CharField(blank=True, choices=[('close_investigation', 'غلق التحقيق'), ('referral', 'احالة'), ('disciplinary_action', 'عقوبة انظباطية')], max_length=30, null=True, verbose_name='مصير المجلس'),
        ),
        migrations.AddField(
            model_name='committee',
            name='file_number',
            field=models.IntegerField(blank=True, null=True, verbose_name='رقم اضبارة الحفظ'),
        ),
        migrations.AddField(
            model_name='committee',
            name='investigation_location',
            field=models.CharField(blank=True, choices=[('headquarters', 'مقر الهيأة'), ('safwan', 'منفذ سفوان'), ('sheeb', 'منفذ الشيب'), ('zurbatiya', 'منفذ زرباطية'), ('mandali', 'منفذ مندلي'), ('munthiriya', 'منفذ المنذرية'), ('rabiaa', 'منفذ ربيعة'), ('qaim', 'منفذ القائم'), ('trebil', 'منفذ طريبيل'), ('arar', 'منفذ عرعر'), ('waleed', 'منفذ الوليد'), ('shalamja', 'منفذ الشلامجة'), ('umm_qasr_north', 'ميناء ام قصر الشمال'), ('umm_qasr_middle', 'ميناء ام قصر الاوسط'), ('umm_qasr_south', 'ميناء ام قصر الجنوبي'), ('abu_flous', 'ميناء ابو فلوس'), ('khor', 'ميناء الخور'), ('maqal', 'ميناء المعقل'), ('baghdad_airport', 'مطار بغداد الدولي'), ('basra_airport', 'مطار البصرة الدولي'), ('kirkuk_airport', 'مطار كركوك الدولي'), ('nasiriya_airport', 'مطار الناصرية الدولي'), ('najaf_airport', 'مطار النجف الدولي')], max_length=50, null=True, verbose_name='محل التحقيق'),
        ),
        migrations.AddField(
            model_name='committee',
            name='issuing_authority',
            field=models.CharField(blank=True, choices=[('headquarters', 'مقر الهيأة'), ('safwan', 'منفذ سفوان'), ('sheeb', 'منفذ الشيب'), ('zurbatiya', 'منفذ زرباطية'), ('mandali', 'منفذ مندلي'), ('munthiriya', 'منفذ المنذرية'), ('rabiaa', 'منفذ ربيعة'), ('qaim', 'منفذ القائم'), ('trebil', 'منفذ طريبيل'), ('arar', 'منفذ عرعر'), ('waleed', 'منفذ الوليد'), ('shalamja', 'منفذ الشلامجة'), ('umm_qasr_north', 'ميناء ام قصر الشمال'), ('umm_qasr_middle', 'ميناء ام قصر الاوسط'), ('umm_qasr_south', 'ميناء ام قصر الجنوبي'), ('abu_flous', 'ميناء ابو فلوس'), ('khor', 'ميناء الخور'), ('maqal', 'ميناء المعقل'), ('baghdad_airport', 'مطار بغداد الدولي'), ('basra_airport', 'مطار البصرة الدولي'), ('kirkuk_airport', 'مطار كركوك الدولي'), ('nasiriya_airport', 'مطار الناصرية الدولي'), ('najaf_airport', 'مطار النجف الدولي')], max_length=50, null=True, verbose_name='جهة اصدار الامر الاداري'),
        ),
        migrations.AddField(
            model_name='committee',
            name='progress_status',
            field=models.CharField(blank=True, choices=[('completed', 'منجز'), ('not_completed', 'غير منجز'), ('under_investigation', 'قيد التحقيق'), ('delayed', 'متأخر')], max_length=30, null=True, verbose_name='تقدم الاجراءات'),
        ),
        migrations.AddField(
            model_name='committee',
            name='violation_type',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='نوع المخالفة'),
        ),
        migrations.AddField(
            model_name='committeemember',
            name='rank',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الرتبة/العنوان الوظيفي'),
        ),
        migrations.AddField(
            model_name='committeemember',
            name='workplace',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='مقر العمل'),
        ),
    ]
