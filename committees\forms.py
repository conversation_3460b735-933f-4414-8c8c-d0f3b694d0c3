from django import forms
from django.forms import formset_factory
from .models import Committee, CommitteeMember
from core.models import File

class CommitteeForm(forms.ModelForm):
    class Meta:
        model = Committee
        fields = [
            'title', 'order_number', 'order_date', 'type', 'responsible_entity',
            'duration', 'end_date', 'last_action', 'notes',
            'issuing_authority', 'file_number',
            'progress_status', 'violation_type', 'accused_rank', 'council_fate',
            'investigation_location'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'order_number': forms.TextInput(attrs={'class': 'form-control'}),
            'order_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'type': forms.Select(attrs={'class': 'form-control'}),
            'responsible_entity': forms.TextInput(attrs={'class': 'form-control'}),
            'duration': forms.NumberInput(attrs={'class': 'form-control', 'onchange': 'updateEndDate()'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'last_action': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'issuing_authority': forms.Select(attrs={'class': 'form-control'}),
            'file_number': forms.NumberInput(attrs={'class': 'form-control'}),
            'progress_status': forms.Select(attrs={'class': 'form-control'}),
            'violation_type': forms.TextInput(attrs={'class': 'form-control'}),
            'accused_rank': forms.TextInput(attrs={'class': 'form-control'}),
            'council_fate': forms.Select(attrs={'class': 'form-control'}),
            'investigation_location': forms.Select(attrs={'class': 'form-control'}),
        }

class CommitteeMemberForm(forms.ModelForm):
    class Meta:
        model = CommitteeMember
        fields = ['name', 'role', 'workplace', 'rank']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'role': forms.Select(attrs={'class': 'form-control'}),
            'workplace': forms.TextInput(attrs={'class': 'form-control'}),
            'rank': forms.TextInput(attrs={'class': 'form-control'}),
        }

class CommitteeExtendForm(forms.Form):
    days = forms.IntegerField(
        label='عدد أيام التمديد',
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

class FileUploadForm(forms.ModelForm):
    class Meta:
        model = File
        fields = ['name', 'file']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }

# إنشاء formset للأعضاء (ديناميكي)
CommitteeMemberFormSet = formset_factory(
    CommitteeMemberForm,
    extra=0,  # لا نحتاج نماذج إضافية (سيتم إضافتها بـ JavaScript)
    min_num=3,  # الحد الأدنى للأعضاء
    max_num=7,  # الحد الأقصى للأعضاء
    validate_min=True,
    validate_max=True,
    can_delete=True  # السماح بحذف الأعضاء
)
