/* تنسيقات القائمة الجانبية */

:root {
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: #343a40;
    --sidebar-color: #f8f9fa;
    --sidebar-hover: #4e555b;
    --sidebar-active: #007bff;
    --header-height: 60px;
    --content-padding: 20px;
}

/* تنسيق الصفحة الرئيسية */
body {
    display: flex;
    min-height: 100vh;
    padding-top: 0 !important;
    overflow-x: hidden;
}

/* القائمة الجانبية */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--sidebar-color);
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

/* شعار النظام */
.sidebar-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: var(--header-height);
}

.sidebar-brand {
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.sidebar-brand:hover {
    color: white;
    text-decoration: none;
}

.sidebar-brand i {
    margin-left: 0.75rem;
    font-size: 1.5rem;
}

/* زر طي القائمة */
.sidebar-toggle {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* قائمة العناصر */
.sidebar-menu {
    padding: 1rem 0;
    list-style: none;
    margin: 0;
}

.sidebar-item {
    margin: 0.25rem 0;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: var(--sidebar-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-link:hover {
    background-color: var(--sidebar-hover);
    color: white;
    text-decoration: none;
}

.sidebar-link.active {
    background-color: var(--sidebar-active);
    color: white;
    font-weight: bold;
}

.sidebar-link i {
    margin-left: 0.75rem;
    width: 1.5rem;
    text-align: center;
    font-size: 1.1rem;
}

.sidebar-link .sidebar-text {
    transition: opacity 0.3s ease;
}

/* القوائم المنسدلة */
.sidebar-dropdown {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.sidebar-dropdown.show {
    max-height: 500px;
}

.sidebar-dropdown-item {
    padding: 0.5rem 1rem 0.5rem 3rem;
    display: block;
    color: rgba(255, 255, 255, 0.75);
    text-decoration: none;
    transition: all 0.2s ease;
}

.sidebar-dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: white;
    text-decoration: none;
}

.sidebar-dropdown-item.active {
    color: white;
    font-weight: bold;
}

.sidebar-dropdown-toggle::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: auto;
    transition: transform 0.3s ease;
}

.sidebar-dropdown-toggle[aria-expanded="true"]::after {
    transform: rotate(180deg);
}

/* قسم المستخدم */
.sidebar-user {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--sidebar-active);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    font-weight: bold;
}

.user-details {
    overflow: hidden;
}

.user-name {
    font-weight: bold;
    margin-bottom: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.user-actions {
    display: flex;
    margin-top: 0.5rem;
}

.user-action {
    flex: 1;
    text-align: center;
    padding: 0.5rem;
    color: var(--sidebar-color);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.user-action:hover {
    background-color: var(--sidebar-hover);
    color: white;
    text-decoration: none;
}

/* الإشعارات */
.notifications-dropdown {
    position: relative;
}

.notifications-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 320px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.notifications-menu.show {
    display: block;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f1f1;
    color: #333;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    min-width: 1.5rem;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    transition: margin 0.3s ease;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* الرأس */
.main-header {
    height: var(--header-height);
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    padding: 0 var(--content-padding);
    position: sticky;
    top: 0;
    z-index: 900;
}

.header-title {
    font-weight: bold;
    margin: 0;
}

.header-actions {
    margin-right: auto;
    display: flex;
    align-items: center;
}

.header-action {
    padding: 0.5rem;
    color: #6c757d;
    position: relative;
    margin-right: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.header-action:hover {
    background-color: #f8f9fa;
    color: #343a40;
}

/* المحتوى */
.content-wrapper {
    padding: var(--content-padding);
    flex: 1;
}

/* تذييل الصفحة */
.main-footer {
    padding: 1rem var(--content-padding);
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

/* حالة القائمة المطوية */
body.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

body.sidebar-collapsed .sidebar-text,
body.sidebar-collapsed .sidebar-brand-text,
body.sidebar-collapsed .user-details,
body.sidebar-collapsed .sidebar-dropdown,
body.sidebar-collapsed .sidebar-dropdown-toggle::after {
    display: none;
}

body.sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
}

body.sidebar-collapsed .sidebar-link {
    justify-content: center;
    padding: 0.75rem;
}

body.sidebar-collapsed .sidebar-link i {
    margin: 0;
    font-size: 1.25rem;
}

body.sidebar-collapsed .user-avatar {
    margin: 0 auto;
}

body.sidebar-collapsed .user-actions {
    flex-direction: column;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
    .sidebar {
        right: calc(-1 * var(--sidebar-width));
        box-shadow: none;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    body.sidebar-mobile-open .sidebar {
        right: 0;
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    body.sidebar-mobile-open .sidebar-overlay {
        display: block;
    }
    
    .mobile-menu-toggle {
        display: block;
        background: transparent;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        margin-left: 1rem;
    }
}
