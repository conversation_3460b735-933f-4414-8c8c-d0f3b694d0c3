from core.models import Notification

class NotificationsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_template_response(self, request, response):
        if hasattr(response, 'context_data') and request.user.is_authenticated:
            # إضافة عدد الإشعارات غير المقروءة
            unread_notifications = Notification.objects.filter(user=request.user, is_read=False).order_by('-created_at')
            response.context_data['unread_notifications_count'] = unread_notifications.count()
            response.context_data['unread_notifications'] = unread_notifications[:5]
        return response
