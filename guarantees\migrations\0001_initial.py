# Generated by Django 5.2 on 2025-04-19 00:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Guarantee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('beneficiary_name', models.CharField(max_length=100, verbose_name='اسم المستفيد')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='مبلغ الكفالة')),
                ('duration', models.PositiveIntegerField(verbose_name='مدة الكفالة (بالأيام)')),
                ('bank_name', models.CharField(max_length=100, verbose_name='اسم المصرف')),
                ('start_date', models.DateField(verbose_name='تاريخ بدء الكفالة')),
                ('end_date', models.DateField(verbose_name='تاريخ انتهاء الكفالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='guarantees', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'كفالة',
                'verbose_name_plural': 'الكفالات',
                'ordering': ['-created_at'],
            },
        ),
    ]
