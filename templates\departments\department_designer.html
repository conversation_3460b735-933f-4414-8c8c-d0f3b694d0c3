{% extends 'base.html' %}

{% block title %}مصمم القسم - {{ department.name }}{% endblock %}

{% block extra_css %}
<style>
    .designer-container {
        display: flex;
        min-height: 600px;
    }
    .fields-panel {
        width: 300px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-right: 20px;
    }
    .canvas-panel {
        flex: 1;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
    }
    .field-item {
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 3px;
        padding: 10px;
        margin-bottom: 10px;
        cursor: move;
    }
    .field-item:hover {
        background-color: #f1f1f1;
    }
    .field-group {
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .field-group-header {
        padding: 10px 15px;
        background-color: #6c757d;
        color: white;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        cursor: move;
    }
    .field-group-body {
        padding: 15px;
        min-height: 50px;
    }
    .field-placeholder {
        border: 2px dashed #ced4da;
        background-color: #f8f9fa;
        height: 40px;
        margin-bottom: 10px;
        border-radius: 3px;
    }
    .group-placeholder {
        border: 2px dashed #6c757d;
        background-color: #e9ecef;
        height: 100px;
        margin-bottom: 20px;
        border-radius: 5px;
    }
    .field-type {
        display: inline-block;
        padding: 2px 5px;
        font-size: 0.8rem;
        border-radius: 3px;
        background-color: #e9ecef;
        margin-right: 5px;
    }
    .field-required {
        color: red;
        margin-right: 5px;
    }
    .field-actions {
        float: left;
    }
    .field-actions a {
        margin-right: 5px;
        color: #6c757d;
    }
    .field-actions a:hover {
        color: #343a40;
    }
    .add-group-btn {
        margin-bottom: 20px;
    }
    .ungrouped-fields {
        margin-top: 20px;
    }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>مصمم القسم</h1>
    <div>
        <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل القسم
        </a>
        <button id="save-design" class="btn btn-primary">
            <i class="fas fa-save"></i> حفظ التصميم
        </button>
    </div>
</div>

<div class="alert alert-info">
    <i class="fas fa-info-circle"></i> استخدم السحب والإفلات لتنظيم الحقول ومجموعات الحقول. يمكنك سحب الحقول بين المجموعات المختلفة.
</div>

<div class="designer-container">
    <div class="fields-panel">
        <h5>الحقول المتاحة</h5>
        <p class="text-muted">اسحب الحقول إلى المجموعات المناسبة</p>
        
        <div id="available-fields">
            {% for field in ungrouped_fields %}
            <div class="field-item" data-field-id="{{ field.id }}">
                <span class="field-type">{{ field.get_type_display }}</span>
                {% if field.required %}<span class="field-required">*</span>{% endif %}
                <strong>{{ field.display_name }}</strong>
                <div class="field-actions">
                    <a href="{% url 'departments:update_field' field.id %}" title="تعديل"><i class="fas fa-edit"></i></a>
                    <a href="{% url 'departments:delete_field' field.id %}" title="حذف"><i class="fas fa-trash"></i></a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="canvas-panel">
        <a href="{% url 'departments:add_field_group' department.id %}" class="btn btn-success add-group-btn">
            <i class="fas fa-plus"></i> إضافة مجموعة جديدة
        </a>
        
        <div id="field-groups-container">
            {% for group in field_groups %}
            <div class="field-group" data-group-id="{{ group.id }}">
                <div class="field-group-header">
                    {{ group.name }}
                    <div class="field-actions">
                        <a href="{% url 'departments:update_field_group' group.id %}" title="تعديل"><i class="fas fa-edit"></i></a>
                        <a href="{% url 'departments:delete_field_group' group.id %}" title="حذف"><i class="fas fa-trash"></i></a>
                    </div>
                </div>
                <div class="field-group-body" data-group-id="{{ group.id }}">
                    {% for field in group.fields.all %}
                    <div class="field-item" data-field-id="{{ field.id }}">
                        <span class="field-type">{{ field.get_type_display }}</span>
                        {% if field.required %}<span class="field-required">*</span>{% endif %}
                        <strong>{{ field.display_name }}</strong>
                        <div class="field-actions">
                            <a href="{% url 'departments:update_field' field.id %}" title="تعديل"><i class="fas fa-edit"></i></a>
                            <a href="{% url 'departments:delete_field' field.id %}" title="حذف"><i class="fas fa-trash"></i></a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="ungrouped-fields">
            <h5>حقول غير مجمعة</h5>
            <div class="field-group-body" id="ungrouped-container">
                <!-- سيتم ملء هذا القسم بالحقول غير المجمعة عن طريق JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>
    $(document).ready(function() {
        // جعل مجموعات الحقول قابلة للسحب والإفلات
        $("#field-groups-container").sortable({
            handle: ".field-group-header",
            placeholder: "group-placeholder",
            update: function(event, ui) {
                // تحديث ترتيب المجموعات
                saveDesign();
            }
        });
        
        // جعل الحقول قابلة للسحب والإفلات
        $(".field-group-body, #available-fields, #ungrouped-container").sortable({
            connectWith: ".field-group-body, #ungrouped-container",
            placeholder: "field-placeholder",
            update: function(event, ui) {
                // تحديث ترتيب الحقول
                saveDesign();
            }
        });
        
        // حفظ التصميم
        $("#save-design").click(function() {
            saveDesign();
        });
        
        function saveDesign() {
            // جمع بيانات المجموعات
            var groups = [];
            $("#field-groups-container .field-group").each(function(index) {
                groups.push($(this).data("group-id"));
            });
            
            // جمع بيانات الحقول
            var fields = [];
            $(".field-group-body, #ungrouped-container").each(function() {
                var groupId = $(this).data("group-id") || null;
                
                $(this).find(".field-item").each(function(index) {
                    fields.push({
                        id: $(this).data("field-id"),
                        group_id: groupId,
                        order: index
                    });
                });
            });
            
            // إرسال البيانات إلى الخادم
            $.ajax({
                url: "{% url 'departments:reorder_fields' department.id %}",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    groups: groups,
                    fields: fields
                }),
                headers: {
                    "X-CSRFToken": "{{ csrf_token }}"
                },
                success: function(response) {
                    if (response.status === "success") {
                        alert("تم حفظ التصميم بنجاح");
                    } else {
                        alert("حدث خطأ أثناء حفظ التصميم: " + response.message);
                    }
                },
                error: function() {
                    alert("حدث خطأ أثناء الاتصال بالخادم");
                }
            });
        }
    });
</script>
{% endblock %}
