{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تفاصيل اللجنة{% endblock %}

{% block content %}
<!-- نافذة تأكيد إغلاق المجلس -->
<div class="modal fade" id="closeCommitteeModal" tabindex="-1" role="dialog" aria-labelledby="closeCommitteeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="closeCommitteeModalLabel">تأكيد إغلاق المجلس</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تحذير:</strong> بعد إغلاق المجلس، لن تتمكن من:
                </div>
                <ul class="text-danger">
                    <li>تعديل بيانات المجلس</li>
                    <li>إضافة أو حذف أعضاء</li>
                    <li>تمديد فترة المجلس</li>
                    <li>رفع مستندات جديدة</li>
                </ul>
                <p>هل أنت متأكد من رغبتك في إغلاق هذا المجلس؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <a href="{% url 'committees:close' committee.id %}" class="btn btn-danger">
                    <i class="fas fa-lock"></i> تأكيد الإغلاق
                </a>
            </div>
        </div>
    </div>
</div>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تفاصيل اللجنة</h1>
    <div>
        <a href="{% url 'committees:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة اللجان
        </a>
        {% if committee.status != 'closed' or user.user_type == 'admin' %}
        <a href="{% url 'committees:update' committee.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        {% endif %}
        {% if committee.status != 'closed' %}
        <a href="{% url 'committees:extend' committee.id %}" class="btn btn-info">
            <i class="fas fa-calendar-plus"></i> تمديد
        </a>
        <a href="#" class="btn btn-danger" id="close-committee-btn">
            <i class="fas fa-lock"></i> إغلاق
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات اللجنة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>العنوان:</strong> {{ committee.title }}</p>
                        <p><strong>رقم الأمر:</strong> {{ committee.order_number }}</p>
                        <p><strong>تاريخ التشكيل:</strong> {{ committee.order_date }}</p>
                        <p><strong>النوع:</strong> {{ committee.get_type_display }}</p>
                        <p><strong>الجهة المسؤولة:</strong> {{ committee.responsible_entity|default:"-" }}</p>
                        <p><strong>جهة إصدار الأمر:</strong> {{ committee.get_issuing_authority_display|default:"-" }}</p>
                        <p><strong>رقم اضبارة الحفظ:</strong> {{ committee.file_number|default:"-" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المدة:</strong> {{ committee.duration }} يوم</p>
                        <p><strong>تاريخ الانتهاء:</strong> {{ committee.end_date }}</p>
                        <p>
                            <strong>الحالة:</strong>
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}

                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </p>
                        <p><strong>رتبة رئيس اللجنة:</strong> {{ committee.chairman_rank|default:"-" }}</p>
                        <p><strong>اسم رئيس اللجنة:</strong> {{ committee.chairman_name|default:"-" }}</p>
                        <p><strong>تقدم الإجراءات:</strong> {{ committee.get_progress_status_display|default:"-" }}</p>
                        <p><strong>تم الإنشاء بواسطة:</strong> {{ committee.created_by.get_full_name|default:committee.created_by.username }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <p><strong>نوع المخالفة:</strong> {{ committee.violation_type|default:"-" }}</p>
                        <p><strong>رتبة المتهم:</strong> {{ committee.accused_rank|default:"-" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>مصير المجلس:</strong> {{ committee.get_council_fate_display|default:"-" }}</p>
                        <p><strong>محل التحقيق:</strong> {{ committee.get_investigation_location_display|default:"-" }}</p>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h6>آخر الإجراءات:</h6>
                        <p>{{ committee.last_action|linebreaks|default:"لا توجد إجراءات مسجلة." }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h6>ملاحظات:</h6>
                        <p>{{ committee.notes|linebreaks|default:"لا توجد ملاحظات." }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أعضاء اللجنة</h5>
                {% if committee.status != 'closed' %}
                <a href="{% url 'committees:add_member' committee.id %}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> إضافة عضو
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>الدور</th>
                                <th>مقر العمل</th>
                                <th>الرتبة/العنوان الوظيفي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in members %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ member.name }}</td>
                                <td>{{ member.get_role_display }}</td>
                                <td>{{ member.workplace|default:"-" }}</td>
                                <td>{{ member.rank|default:"-" }}</td>
                                <td>
                                    {% if committee.status != 'closed' %}
                                    <a href="{% url 'committees:delete_member' member.id %}" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">لا يوجد أعضاء مسجلين.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المستندات</h5>
                {% if committee.status != 'closed' or user.user_type == 'admin' %}
                <a href="{% url 'committees:upload_document' committee.id %}" class="btn btn-sm btn-light">
                    <i class="fas fa-upload"></i> رفع مستند
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for file in files %}
                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <a href="{{ file.file.url }}" target="_blank">{{ file.name }}</a>
                            <small class="d-block text-muted">{{ file.uploaded_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        <div class="btn-group">
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-primary" download>
                                <i class="fas fa-download" title="تحميل"></i>
                            </a>
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-info" target="_blank">
                                <i class="fas fa-eye" title="عرض"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-secondary print-file" data-url="{{ file.file.url }}">
                                <i class="fas fa-print" title="طباعة"></i>
                            </button>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-center">لا توجد مستندات مرفقة.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">معلومات النظام</h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong> {{ committee.created_at|date:"Y-m-d H:i" }}</p>
                <p><strong>تاريخ آخر تحديث:</strong> {{ committee.updated_at|date:"Y-m-d H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // معالجة زر إغلاق المجلس
        $('#close-committee-btn').on('click', function(e) {
            e.preventDefault();
            $('#closeCommitteeModal').modal('show');
        });

        // معالجة زر الطباعة
        $('.print-file').on('click', function() {
            const fileUrl = $(this).data('url');

            // إنشاء إطار مخفي للطباعة
            const printFrame = $('<iframe>', {
                name: 'printFrame',
                class: 'print-frame',
                style: 'position:absolute;width:0;height:0;left:-1000px;top:-1000px;'
            }).appendTo('body');

            // تحميل الملف في الإطار ثم طباعته
            printFrame.on('load', function() {
                try {
                    // محاولة الطباعة
                    frames['printFrame'].focus();
                    frames['printFrame'].print();

                    // إزالة الإطار بعد الطباعة
                    setTimeout(function() {
                        printFrame.remove();
                    }, 1000);
                } catch (e) {
                    // في حالة حدوث خطأ، افتح الملف في نافذة جديدة للطباعة
                    alert('لا يمكن طباعة هذا الملف مباشرة. سيتم فتحه في نافذة جديدة للطباعة.');
                    window.open(fileUrl, '_blank');
                }
            });

            // تعيين مصدر الإطار
            printFrame.attr('src', fileUrl);
        });
    });
</script>
{% endblock %}