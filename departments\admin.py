from django.contrib import admin
from .models import Department, FieldGroup, Field, DepartmentData, DepartmentDataHistory

class FieldGroupInline(admin.TabularInline):
    model = FieldGroup
    extra = 1
    show_change_link = True

class FieldInline(admin.TabularInline):
    model = Field
    extra = 1
    show_change_link = True
    fields = ('name', 'display_name', 'type', 'required', 'order')

class DepartmentDataHistoryInline(admin.TabularInline):
    model = DepartmentDataHistory
    extra = 0
    readonly_fields = ('action', 'user', 'timestamp', 'data_snapshot')
    can_delete = False
    max_num = 0
    show_change_link = True

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'manager', 'access_level', 'show_in_menu', 'is_active', 'created_at')
    list_filter = ('is_active', 'access_level', 'show_in_menu')
    search_fields = ('name', 'description')
    filter_horizontal = ('authorized_users',)
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'table_name')
        }),
        ('المظهر', {
            'fields': ('icon', 'color', 'order', 'show_in_menu')
        }),
        ('الإدارة والصلاحيات', {
            'fields': ('manager', 'access_level', 'authorized_users', 'is_active')
        }),
    )
    inlines = [FieldGroupInline, FieldInline]

@admin.register(FieldGroup)
class FieldGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'department', 'order', 'is_collapsed')
    list_filter = ('department', 'is_collapsed')
    search_fields = ('name', 'description')
    ordering = ('department', 'order', 'name')
    inlines = [FieldInline]

@admin.register(Field)
class FieldAdmin(admin.ModelAdmin):
    list_display = ('name', 'display_name', 'department', 'group', 'type', 'required', 'order')
    list_filter = ('department', 'group', 'type', 'required', 'is_searchable', 'is_filterable')
    search_fields = ('name', 'display_name', 'description')
    ordering = ('department', 'group', 'order', 'name')
    fieldsets = (
        (None, {
            'fields': ('department', 'group', 'name', 'display_name', 'description', 'type')
        }),
        ('خصائص الحقل', {
            'fields': ('required', 'default_value', 'placeholder', 'help_text', 'options')
        }),
        ('التحقق', {
            'fields': ('validation_type', 'validation_params', 'is_unique')
        }),
        ('العرض', {
            'fields': ('is_searchable', 'is_filterable', 'show_in_table', 'show_in_detail', 'show_in_export',
                      'is_readonly', 'is_hidden', 'order', 'css_class', 'width')
        }),
    )

@admin.register(DepartmentData)
class DepartmentDataAdmin(admin.ModelAdmin):
    list_display = ('title', 'department', 'reference_number', 'status', 'created_by', 'created_at')
    list_filter = ('department', 'status', 'created_at')
    search_fields = ('title', 'reference_number', 'data')
    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')
    inlines = [DepartmentDataHistoryInline]

@admin.register(DepartmentDataHistory)
class DepartmentDataHistoryAdmin(admin.ModelAdmin):
    list_display = ('data_entry', 'action', 'user', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('data_entry__title', 'user__username')
    readonly_fields = ('data_entry', 'data_snapshot', 'action', 'user', 'timestamp')
