{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
{% if group %}تعديل مجموعة الحقول{% else %}إضافة مجموعة حقول جديدة{% endif %} - {{ department.name }}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if group %}تعديل مجموعة الحقول{% else %}إضافة مجموعة حقول جديدة{% endif %}</h1>
    <div>
        <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل القسم
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ department.name }}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            {{ form|crispy }}
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
