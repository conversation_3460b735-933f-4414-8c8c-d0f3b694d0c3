from .models import Department

def custom_departments(request):
    """إضافة الأقسام المخصصة إلى سياق القالب"""
    if request.user.is_authenticated:
        # الحصول على الأقسام النشطة التي يجب عرضها في القائمة
        departments = Department.objects.filter(is_active=True, show_in_menu=True).order_by('order')
        
        # تصفية الأقسام حسب صلاحيات المستخدم
        user_departments = []
        for dept in departments:
            if dept.can_user_access(request.user):
                user_departments.append(dept)
        
        return {'custom_departments': user_departments}
    
    return {'custom_departments': []}
