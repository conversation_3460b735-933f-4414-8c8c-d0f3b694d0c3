{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}إضافة عضو للجنة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إضافة عضو للجنة</h1>
    <a href="{% url 'committees:detail' committee.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى تفاصيل اللجنة
    </a>
</div>

<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">إضافة عضو للجنة "{{ committee.title }}"</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.role.id_for_label }}">{{ form.role.label }}</label>
                {{ form.role }}
                {% if form.role.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.role.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'committees:detail' committee.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
