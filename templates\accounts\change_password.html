{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تغيير كلمة المرور{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تغيير كلمة المرور</h1>
    <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى الملف الشخصي
    </a>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">تغيير كلمة المرور</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="form-group">
                        <label for="{{ form.old_password.id_for_label }}">{{ form.old_password.label }}</label>
                        {{ form.old_password }}
                        {% if form.old_password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.old_password.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.new_password1.id_for_label }}">{{ form.new_password1.label }}</label>
                        {{ form.new_password1 }}
                        {% if form.new_password1.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.new_password1.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        {% if form.new_password1.help_text %}
                        <small class="form-text text-muted">{{ form.new_password1.help_text }}</small>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.new_password2.id_for_label }}">{{ form.new_password2.label }}</label>
                        {{ form.new_password2 }}
                        {% if form.new_password2.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.new_password2.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </button>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
