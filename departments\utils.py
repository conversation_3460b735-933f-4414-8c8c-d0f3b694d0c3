import json
import datetime
import logging
from django.db import connection

logger = logging.getLogger(__name__)

class DateTimeJSONEncoder(json.JSONEncoder):
    """مُحوِّل JSON مخصص لمعالجة كائنات التاريخ والوقت"""
    def default(self, obj):
        if isinstance(obj, (datetime.date, datetime.datetime)):
            return obj.isoformat()
        return super().default(obj)

def date_json_serializer(obj):
    """دالة مساعدة لتحويل كائنات التاريخ والوقت إلى سلاسل نصية"""
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def create_department_table(table_name):
    """إنشاء جدول جديد في قاعدة البيانات"""
    cursor = connection.cursor()

    try:
        # تحديد نوع قاعدة البيانات
        db_engine = connection.vendor

        # التحقق من وجود الجدول
        if db_engine == 'sqlite':
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        else:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")

        table_exists = bool(cursor.fetchone())

        if not table_exists:
            # إنشاء الجدول إذا لم يكن موجودًا
            if db_engine == 'sqlite':
                # استعلام SQLite
                create_table_sql = f"""
                CREATE TABLE {table_name} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    reference_number TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by_id INTEGER,
                    updated_by_id INTEGER
                )
                """
            else:
                # استعلام MySQL/MariaDB
                create_table_sql = f"""
                CREATE TABLE {table_name} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    reference_number VARCHAR(100),
                    status VARCHAR(50) DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    created_by_id INT,
                    updated_by_id INT
                )
                """

            cursor.execute(create_table_sql)
            logger.info(f"تم إنشاء الجدول {table_name} بنجاح")
            return True
        else:
            logger.info(f"الجدول {table_name} موجود بالفعل")
            return False
    except Exception as e:
        logger.error(f"خطأ في إنشاء الجدول {table_name}: {str(e)}")
        raise

def add_field_to_table(table_name, field_name, field_type, required=False):
    """إضافة حقل جديد إلى جدول"""
    cursor = connection.cursor()

    try:
        # تحديد نوع قاعدة البيانات
        db_engine = connection.vendor

        # التحقق من وجود الحقل
        if db_engine == 'sqlite':
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [column[1] for column in cursor.fetchall()]
            field_exists = field_name in columns
        else:
            cursor.execute(f"SHOW COLUMNS FROM {table_name} LIKE '{field_name}'")
            field_exists = bool(cursor.fetchone())

        if not field_exists:
            # تحديد نوع الحقل في قاعدة البيانات
            field_type_map = {
                'text': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                'textarea': 'TEXT',
                'rich_text': 'TEXT',
                'email': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                'url': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                'phone': 'VARCHAR(50)' if db_engine != 'sqlite' else 'TEXT',
                'number': 'INT' if db_engine != 'sqlite' else 'INTEGER',
                'decimal': 'DECIMAL(15, 2)' if db_engine != 'sqlite' else 'REAL',
                'currency': 'DECIMAL(15, 2)' if db_engine != 'sqlite' else 'REAL',
                'percentage': 'DECIMAL(5, 2)' if db_engine != 'sqlite' else 'REAL',
                'date': 'DATE',
                'time': 'TIME',
                'datetime': 'DATETIME',
                'select': 'VARCHAR(100)' if db_engine != 'sqlite' else 'TEXT',
                'multi_select': 'TEXT',
                'radio': 'VARCHAR(100)' if db_engine != 'sqlite' else 'TEXT',
                'checkbox': 'BOOLEAN' if db_engine != 'sqlite' else 'INTEGER',
                'file': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                'image': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                'pdf': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
            }

            sql_type = field_type_map.get(field_type, 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT')
            nullable = 'NOT NULL' if required else 'NULL'

            # إضافة الحقل إلى الجدول
            alter_table_sql = f"ALTER TABLE {table_name} ADD COLUMN {field_name} {sql_type} {nullable}"
            cursor.execute(alter_table_sql)
            logger.info(f"تم إضافة الحقل {field_name} إلى الجدول {table_name} بنجاح")
            return True
        else:
            logger.info(f"الحقل {field_name} موجود بالفعل في الجدول {table_name}")
            return False
    except Exception as e:
        logger.error(f"خطأ في إضافة الحقل {field_name} إلى الجدول {table_name}: {str(e)}")
        raise

def get_table_data(table_name, filters=None, order_by=None, limit=None, offset=None):
    """الحصول على بيانات من جدول"""
    cursor = connection.cursor()

    try:
        # بناء استعلام SQL
        sql = f"SELECT * FROM {table_name}"

        # إضافة شروط التصفية
        params = []
        if filters:
            where_clauses = []
            for field, value in filters.items():
                where_clauses.append(f"{field} = %s")
                params.append(value)

            if where_clauses:
                sql += " WHERE " + " AND ".join(where_clauses)

        # إضافة ترتيب
        if order_by:
            sql += f" ORDER BY {order_by}"

        # إضافة حد وإزاحة
        if limit:
            sql += f" LIMIT {limit}"
            if offset:
                sql += f" OFFSET {offset}"

        # تنفيذ الاستعلام
        cursor.execute(sql, params)

        # الحصول على أسماء الأعمدة
        columns = [col[0] for col in cursor.description]

        # تحويل النتائج إلى قائمة من القواميس
        results = []
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        return results
    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات من الجدول {table_name}: {str(e)}")
        raise

def insert_table_data(table_name, data):
    """إدخال بيانات في جدول"""
    cursor = connection.cursor()

    try:
        # استخراج أسماء الحقول والقيم
        fields = list(data.keys())
        values = list(data.values())

        # بناء استعلام SQL
        placeholders = ", ".join(["%s"] * len(fields))
        sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({placeholders})"

        # تنفيذ الاستعلام
        cursor.execute(sql, values)

        # الحصول على معرف السجل المدخل
        db_engine = connection.vendor
        if db_engine == 'sqlite':
            cursor.execute("SELECT last_insert_rowid()")
        else:
            cursor.execute("SELECT LAST_INSERT_ID()")

        inserted_id = cursor.fetchone()[0]

        return inserted_id
    except Exception as e:
        logger.error(f"خطأ في إدخال بيانات في الجدول {table_name}: {str(e)}")
        raise

def export_table_to_excel(table_name, fields, data_entries):
    """تصدير بيانات الجدول إلى Excel"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill

        # إنشاء مصنف جديد وورقة عمل
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = table_name

        # إضافة رأس الجدول
        header = ['العنوان', 'الرقم المرجعي', 'الحالة', 'تاريخ الإنشاء']
        for field in fields:
            header.append(field.display_name)

        # تنسيق رأس الجدول
        for col_num, column_title in enumerate(header, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.value = column_title
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color='DDDDDD', end_color='DDDDDD', fill_type='solid')

        # إضافة البيانات
        for row_num, entry in enumerate(data_entries, 2):
            ws.cell(row=row_num, column=1).value = entry.title
            ws.cell(row=row_num, column=2).value = entry.reference_number or ''
            ws.cell(row=row_num, column=3).value = entry.status
            ws.cell(row=row_num, column=4).value = entry.created_at.strftime('%Y-%m-%d %H:%M')

            for col_num, field in enumerate(fields, 5):
                value = entry.get_data_value(field.name) if entry.data else ''
                ws.cell(row=row_num, column=col_num).value = value or ''

        # تنسيق الجدول
        for col_num in range(1, len(header) + 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col_num)].width = 20

        return wb
    except Exception as e:
        logger.error(f"خطأ في تصدير بيانات الجدول {table_name} إلى Excel: {str(e)}")
        raise
