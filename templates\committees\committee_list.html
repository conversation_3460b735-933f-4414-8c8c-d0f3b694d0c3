{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}قائمة اللجان{% endblock %}

{% block content %}
<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 10px;">
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1>قائمة اللجان</h1>
    <div>
        <button class="btn btn-secondary" id="toggle-columns">
            <i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة
        </button>
        <a href="{% url 'committees:export_excel' %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </a>
        {% if user.user_type == 'admin' %}
        <a href="{% url 'committees:import_excel' %}" class="btn btn-info">
            <i class="fas fa-file-upload"></i> استيراد Excel
        </a>
        {% endif %}
        <a href="{% url 'committees:create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة لجنة جديدة
        </a>
    </div>
</div>

<!-- قائمة إظهار/إخفاء الأعمدة -->
<div class="card mb-3" id="columns-control" style="display: none;">
    <div class="card-body">
        <h6>إظهار/إخفاء الأعمدة:</h6>
        <div class="row">

            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-order" checked>
                    <label class="form-check-label" for="col-order">رقم الأمر</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-date" checked>
                    <label class="form-check-label" for="col-date">تاريخ التشكيل</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-type" checked>
                    <label class="form-check-label" for="col-type">النوع</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman-name" checked>
                    <label class="form-check-label" for="col-chairman-name">اسم رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman-rank" checked>
                    <label class="form-check-label" for="col-chairman-rank">رتبة رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-authority">
                    <label class="form-check-label" for="col-authority">جهة الإصدار</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-file">
                    <label class="form-check-label" for="col-file">رقم الاضبارة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-progress">
                    <label class="form-check-label" for="col-progress">تقدم الإجراءات</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-violation">
                    <label class="form-check-label" for="col-violation">نوع المخالفة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-accused">
                    <label class="form-check-label" for="col-accused">رتبة المتهم</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-fate">
                    <label class="form-check-label" for="col-fate">مصير المجلس</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-location">
                    <label class="form-check-label" for="col-location">محل التحقيق</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-status" checked>
                    <label class="form-check-label" for="col-status">الحالة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-end-date" checked>
                    <label class="form-check-label" for="col-end-date">تاريخ الانتهاء</label>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">اللجان والمجالس</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="committees-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th class="col-order">رقم الأمر</th>
                        <th class="col-date">تاريخ التشكيل</th>
                        <th class="col-type">النوع</th>
                        <th class="col-chairman-name">اسم رئيس اللجنة</th>
                        <th class="col-chairman-rank">رتبة رئيس اللجنة</th>
                        <th class="col-authority">جهة الإصدار</th>
                        <th class="col-file">رقم الاضبارة</th>
                        <th class="col-progress">تقدم الإجراءات</th>
                        <th class="col-violation">نوع المخالفة</th>
                        <th class="col-accused">رتبة المتهم</th>
                        <th class="col-fate">مصير المجلس</th>
                        <th class="col-location">محل التحقيق</th>
                        <th class="col-status">الحالة</th>
                        <th class="col-end-date">تاريخ الانتهاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for committee in committees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td class="col-order">{{ committee.order_number }}</td>
                        <td class="col-date">{{ committee.order_date }}</td>
                        <td class="col-type">{{ committee.get_type_display }}</td>
                        <td class="col-chairman-name">{{ committee.chairman_name|default:"-" }}</td>
                        <td class="col-chairman-rank">{{ committee.chairman_rank|default:"-" }}</td>
                        <td class="col-authority">{{ committee.get_issuing_authority_display|default:"-" }}</td>
                        <td class="col-file">{{ committee.file_number|default:"-" }}</td>
                        <td class="col-progress">
                            {% if committee.progress_status %}
                                {% if committee.progress_status == 'completed' %}
                                <span class="badge badge-success">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'not_completed' %}
                                <span class="badge badge-danger">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'under_investigation' %}
                                <span class="badge badge-warning">{{ committee.get_progress_status_display }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ committee.get_progress_status_display }}</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="col-violation">{{ committee.violation_type|default:"-" }}</td>
                        <td class="col-accused">{{ committee.accused_rank|default:"-" }}</td>
                        <td class="col-fate">{{ committee.get_council_fate_display|default:"-" }}</td>
                        <td class="col-location">{{ committee.get_investigation_location_display|default:"-" }}</td>
                        <td class="col-status">
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td class="col-end-date">
                            {{ committee.end_date }}
                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'committees:detail' committee.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'committees:update' committee.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.user_type == 'admin' %}
                                <a href="{% url 'committees:delete' committee.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="16" class="text-center">لا توجد لجان مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* منع خروج أي عنصر من الصفحة نهائياً */
    html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    .container {
        overflow-x: hidden !important;
        max-width: 1200px !important;
        width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
        margin: 0 auto !important;
    }

    /* منع أي عنصر من الخروج من viewport */
    * {
        box-sizing: border-box !important;
    }

    /* حماية شاملة من الخروج */
    .main-content, .content-wrapper, .page-content {
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    /* حماية خاصة للجداول */
    .table-container, .table-wrapper {
        overflow-x: hidden !important;
        max-width: 100% !important;
    }
    /* إجبار الجدول على البقاء داخل الإطار نهائياً */
    .table-responsive {
        max-height: 70vh;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: scroll !important; /* مسطرة أفقية إجبارية */
        overflow-y: auto !important; /* مسطرة عمودية */
        position: relative;
        box-sizing: border-box !important;
        display: block !important;
        margin: 0 !important;
    }

    /* تخصيص المسطرة */
    .table-responsive::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #007bff;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #0056b3;
    }

    /* زاوية المسطرة */
    .table-responsive::-webkit-scrollbar-corner {
        background: #f1f1f1;
    }

    #committees-table {
        min-width: 800px; /* عرض أدنى ثابت */
        width: max-content; /* عرض حسب المحتوى */
        max-width: none; /* السماح بالتوسع داخل الحاوي فقط */
        margin: 0;
        border-collapse: collapse;
        table-layout: auto;
        font-size: 14px;
    }

    #committees-table th,
    #committees-table td {
        white-space: nowrap;
        min-width: 100px; /* عرض أدنى أصغر لكل عمود */
    }

    /* عرض مخصص للأعمدة المهمة */
    .col-title {
        min-width: 150px !important;
        max-width: 200px;
    }

    .col-status {
        min-width: 60px !important;
        width: 60px;
    }

    .col-actions {
        min-width: 80px !important;
        width: 80px;
    }

    /* تقليل عرض الأعمدة الأخرى */
    .col-order, .col-date, .col-type, .col-chairman-name, .col-chairman-rank {
        min-width: 70px !important;
        max-width: 120px;
    }

    /* الأعمدة الثابتة - فقط عند وجود مسطرة أفقية */
    .table-responsive.has-horizontal-scroll #committees-table th:first-child,
    .table-responsive.has-horizontal-scroll #committees-table td:first-child {
        position: sticky;
        left: 0;
        background: white;
        z-index: 2;
        width: 50px;
        min-width: 50px;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    .table-responsive.has-horizontal-scroll #committees-table th:last-child,
    .table-responsive.has-horizontal-scroll #committees-table td:last-child {
        position: sticky;
        right: 0;
        background: white;
        z-index: 2;
        width: 120px;
        min-width: 120px;
        box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    }

    /* رؤوس الأعمدة الثابتة */
    .table-responsive.has-horizontal-scroll #committees-table thead th:first-child {
        background-color: #007bff;
        z-index: 12;
    }

    .table-responsive.has-horizontal-scroll #committees-table thead th:last-child {
        background-color: #007bff;
        z-index: 12;
    }

    /* رؤوس الأعمدة */
    #committees-table thead th {
        background-color: #007bff;
        color: white;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* خلايا الجدول */
    #committees-table th,
    #committees-table td {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 14px;
        line-height: 1.4;
    }

    /* الشارات */
    .badge {
        font-size: 11px;
    }

    /* تحسين عرض الأزرار */
    .btn-group .btn {
        margin: 1px;
        padding: 4px 8px;
        font-size: 12px;
        line-height: 1.4;
    }

    .btn-sm {
        padding: 4px 6px;
        font-size: 11px;
    }



    /* إخفاء الأعمدة الإضافية بشكل افتراضي */
    .col-authority,
    .col-file,
    .col-progress,
    .col-violation,
    .col-accused,
    .col-fate,
    .col-location {
        display: none;
    }

    /* الأعمدة الأساسية تظهر دائماً */
    .col-order,
    .col-date,
    .col-type,
    .col-chairman-name,
    .col-chairman-rank,
    .col-status,
    .col-end-date {
        display: table-cell;
    }

    /* منع الجدول من الخروج من الحاوي نهائياً */
    .card {
        overflow: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    .card-body {
        overflow: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 15px !important;
        box-sizing: border-box !important;
    }

    /* إجبار الجدول على البقاء داخل الحاوي */
    .table-responsive {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: scroll !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch;
        margin: 0 !important;
        position: relative !important;
        box-sizing: border-box !important;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحميل حالة الأعمدة المحفوظة
    loadColumnState();

    // إظهار/إخفاء قائمة التحكم في الأعمدة
    $('#toggle-columns').click(function() {
        $('#columns-control').toggle();
    });

    // التحكم في إظهار/إخفاء الأعمدة
    $('.column-toggle').change(function() {
        var columnClass = $(this).attr('id');
        var isChecked = $(this).is(':checked');

        if (isChecked) {
            $('.' + columnClass).css('display', 'table-cell');
        } else {
            $('.' + columnClass).css('display', 'none');
        }

        // تحديث عرض الجدول بناءً على عدد الأعمدة المرئية
        updateTableWidth();

        // حفظ الحالة
        saveColumnState();
    });

    // دالة حفظ حالة الأعمدة
    function saveColumnState() {
        var columnStates = {};
        $('.column-toggle').each(function() {
            columnStates[$(this).attr('id')] = $(this).is(':checked');
        });
        localStorage.setItem('committeeColumnsState', JSON.stringify(columnStates));
    }

    // دالة تحديث عرض الجدول
    function updateTableWidth() {
        // عد الأعمدة المرئية الإجمالية
        var visibleColumns = 0;
        $('.column-toggle').each(function() {
            if ($(this).is(':checked')) {
                visibleColumns++;
            }
        });

        // إضافة الأعمدة الأساسية (الرقم والإجراءات)
        var totalColumns = visibleColumns + 2; // +2 للرقم والإجراءات

        // تحديد العرض بناءً على عدد الأعمدة
        if (totalColumns > 7) {
            // إذا كان هناك أكثر من 7 أعمدة، زيادة عرض الجدول
            var minWidth = totalColumns * 130; // 130px لكل عمود
            $('#committees-table').css({
                'min-width': minWidth + 'px',
                'width': 'max-content'
            });

            // تفعيل المسطرة الأفقية
            $('.table-responsive').addClass('has-horizontal-scroll');
        } else {
            // إذا كان 7 أعمدة أو أقل، استخدم العرض الطبيعي
            $('#committees-table').css({
                'min-width': '800px',
                'width': '100%'
            });
            $('.table-responsive').removeClass('has-horizontal-scroll');
        }

        // إجبار إعادة حساب العرض
        $('.table-responsive').trigger('scroll');
    }



    // دالة تحميل حالة الأعمدة
    function loadColumnState() {
        var savedStates = localStorage.getItem('committeeColumnsState');
        if (savedStates) {
            var columnStates = JSON.parse(savedStates);
            $.each(columnStates, function(columnId, isVisible) {
                $('#' + columnId).prop('checked', isVisible);
                if (isVisible) {
                    $('.' + columnId).css('display', 'table-cell');
                } else {
                    $('.' + columnId).css('display', 'none');
                }
            });
        }
    }

    // تحديث عرض الجدول عند تحميل الصفحة
    updateTableWidth();
});
</script>
{% endblock %}
