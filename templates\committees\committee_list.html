{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}قائمة اللجان{% endblock %}

{% block content %}
<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 10px;">
<div class="d-flex justify-content-between align-items-center mb-3">
    <h1>قائمة اللجان</h1>
    <div>
        <button class="btn btn-secondary" id="toggle-columns">
            <i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة
        </button>
        <a href="{% url 'committees:export_excel' %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </a>
        {% if user.user_type == 'admin' %}
        <a href="{% url 'committees:import_excel' %}" class="btn btn-info">
            <i class="fas fa-file-upload"></i> استيراد Excel
        </a>
        {% endif %}
        <a href="{% url 'committees:create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة لجنة جديدة
        </a>
    </div>
</div>

<!-- شريط البحث السريع -->
<div class="card mb-3">
    <div class="card-body py-3">
        <form method="GET" class="row align-items-center">
            <!-- البحث السريع -->
            <div class="col-md-8">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                    </div>
                    <input type="text" name="search" class="form-control"
                           placeholder="بحث في رقم الأمر، اسم رئيس اللجنة، أو رتبة رئيس اللجنة..."
                           value="{{ request.GET.search }}">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div class="col-md-4 text-right">
                {% if request.GET.search %}
                    <a href="{% url 'committees:list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> مسح البحث
                    </a>
                {% endif %}
                <button type="button" class="btn btn-info" onclick="toggleAdvancedFilters()">
                    <i class="fas fa-filter"></i> فلاتر متقدمة
                </button>
            </div>

            <!-- الحفاظ على معاملات أخرى -->
            <input type="hidden" name="per_page" value="{{ per_page }}">
        </form>
    </div>
</div>

<!-- الفلاتر المتقدمة -->
<div class="card mb-3" id="advanced-filters" style="display: none;">
    <div class="card-header bg-light">
        <h6 class="mb-0">
            <i class="fas fa-filter"></i> فلاتر متقدمة
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" id="advanced-filters-form">
            <div class="row">
                <!-- النوع -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_type">النوع:</label>
                        <select name="type" id="filter_type" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="investigation" {% if request.GET.type == 'investigation' %}selected{% endif %}>لجنة تحقيقية</option>
                            <option value="council" {% if request.GET.type == 'council' %}selected{% endif %}>مجلس تحقيقي</option>
                            <option value="joint" {% if request.GET.type == 'joint' %}selected{% endif %}>لجنة مشتركة</option>
                        </select>
                    </div>
                </div>

                <!-- الحالة -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_status">الحالة:</label>
                        <select name="status" id="filter_status" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشطة</option>
                            <option value="extended" {% if request.GET.status == 'extended' %}selected{% endif %}>ممددة</option>
                            <option value="closed" {% if request.GET.status == 'closed' %}selected{% endif %}>مغلقة</option>
                        </select>
                    </div>
                </div>

                <!-- جهة إصدار الأمر -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_authority">جهة إصدار الأمر:</label>
                        <select name="issuing_authority" id="filter_authority" class="form-control">
                            <option value="">جميع الجهات</option>
                            {% for authority_key, authority_name in available_authorities %}
                                <option value="{{ authority_key }}" {% if request.GET.issuing_authority == authority_key %}selected{% endif %}>
                                    {{ authority_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <!-- تاريخ التشكيل من -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_date_from">تاريخ التشكيل من:</label>
                        <input type="date" name="date_from" id="filter_date_from" class="form-control"
                               value="{{ request.GET.date_from }}">
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- تاريخ التشكيل إلى -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_date_to">تاريخ التشكيل إلى:</label>
                        <input type="date" name="date_to" id="filter_date_to" class="form-control"
                               value="{{ request.GET.date_to }}">
                    </div>
                </div>

                <!-- تاريخ الانتهاء من -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_end_date_from">تاريخ الانتهاء من:</label>
                        <input type="date" name="end_date_from" id="filter_end_date_from" class="form-control"
                               value="{{ request.GET.end_date_from }}">
                    </div>
                </div>

                <!-- تاريخ الانتهاء إلى -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="filter_end_date_to">تاريخ الانتهاء إلى:</label>
                        <input type="date" name="end_date_to" id="filter_end_date_to" class="form-control"
                               value="{{ request.GET.end_date_to }}">
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary mr-2">
                                <i class="fas fa-search"></i> تطبيق الفلاتر
                            </button>
                            <a href="{% url 'committees:list' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح الكل
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الحفاظ على معاملات أخرى -->
            <input type="hidden" name="search" value="{{ request.GET.search }}">
            <input type="hidden" name="per_page" value="{{ per_page }}">
        </form>
    </div>
</div>

<!-- الفلاتر السريعة -->
<div class="card mb-3">
    <div class="card-body py-2">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex flex-wrap">
                    <span class="mr-3 align-self-center font-weight-bold">فلاتر سريعة:</span>
                    <a href="?status=active&per_page={{ per_page }}" class="btn btn-success btn-sm mr-2 mb-1">
                        <i class="fas fa-play-circle"></i> نشطة
                    </a>
                    <a href="?status=closed&per_page={{ per_page }}" class="btn btn-danger btn-sm mr-2 mb-1">
                        <i class="fas fa-stop-circle"></i> منتهية
                    </a>
                    <a href="?quick_filter=expiring_soon&per_page={{ per_page }}" class="btn btn-warning btn-sm mr-2 mb-1">
                        <i class="fas fa-clock"></i> ستنتهي قريباً
                    </a>
                    <a href="?status=extended&per_page={{ per_page }}" class="btn btn-info btn-sm mr-2 mb-1">
                        <i class="fas fa-plus-circle"></i> ممددة
                    </a>
                    <a href="?type=investigation&per_page={{ per_page }}" class="btn btn-primary btn-sm mr-2 mb-1">
                        <i class="fas fa-search"></i> لجان تحقيقية
                    </a>
                    <a href="?type=council&per_page={{ per_page }}" class="btn btn-secondary btn-sm mr-2 mb-1">
                        <i class="fas fa-users"></i> مجالس تحقيقية
                    </a>
                </div>
            </div>
            <div class="col-md-4 text-right">
                {% if request.GET %}
                    <a href="{% url 'committees:list' %}?per_page={{ per_page }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times"></i> مسح جميع الفلاتر
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قائمة إظهار/إخفاء الأعمدة -->
<div class="card mb-3" id="columns-control" style="display: none;">
    <div class="card-body">
        <h6>إظهار/إخفاء الأعمدة:</h6>
        <div class="row">

            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-order" checked>
                    <label class="form-check-label" for="col-order">رقم الأمر</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-date" checked>
                    <label class="form-check-label" for="col-date">تاريخ التشكيل</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-type" checked>
                    <label class="form-check-label" for="col-type">النوع</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman-name" checked>
                    <label class="form-check-label" for="col-chairman-name">اسم رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman-rank" checked>
                    <label class="form-check-label" for="col-chairman-rank">رتبة رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-authority">
                    <label class="form-check-label" for="col-authority">جهة الإصدار</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-file">
                    <label class="form-check-label" for="col-file">رقم الاضبارة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-progress">
                    <label class="form-check-label" for="col-progress">تقدم الإجراءات</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-violation">
                    <label class="form-check-label" for="col-violation">نوع المخالفة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-accused">
                    <label class="form-check-label" for="col-accused">رتبة المتهم</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-fate">
                    <label class="form-check-label" for="col-fate">مصير المجلس</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-location">
                    <label class="form-check-label" for="col-location">محل التحقيق</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-subject">
                    <label class="form-check-label" for="col-subject">الموضوع</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-status" checked>
                    <label class="form-check-label" for="col-status">الحالة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-end-date" checked>
                    <label class="form-check-label" for="col-end-date">تاريخ الانتهاء</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- شريط Pagination والتحكم -->
<div class="card mb-3">
    <div class="card-body py-2">
        <div class="row align-items-center">
            <!-- عدد النتائج -->
            <div class="col-md-3">
                <div class="d-flex align-items-center">
                    <label for="per_page" class="mb-0 mr-2">عرض:</label>
                    <select id="per_page" class="form-control form-control-sm" style="width: auto;" onchange="changePerPage()">
                        <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                        <option value="25" {% if per_page == 25 %}selected{% endif %}>25</option>
                        <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                    <span class="mb-0 ml-2">نتيجة</span>
                </div>
            </div>

            <!-- معلومات النتائج -->
            <div class="col-md-6 text-center">
                <span class="text-muted">
                    عرض {{ page_obj.start_index }}-{{ page_obj.end_index }} من أصل {{ total_count }} نتيجة
                </span>
            </div>

            <!-- زر التصدير -->
            <div class="col-md-3 text-right">
                <a href="#" id="export-btn" class="btn btn-success btn-sm" onclick="exportWithVisibleColumns()">
                    <i class="fas fa-file-excel"></i> تصدير النتائج
                    {% if total_count < 100 %}({{ total_count }}){% endif %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">اللجان والمجالس</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="committees-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th class="col-order">رقم الأمر</th>
                        <th class="col-date">تاريخ التشكيل</th>
                        <th class="col-type">النوع</th>
                        <th class="col-chairman-name">اسم رئيس اللجنة</th>
                        <th class="col-chairman-rank">رتبة رئيس اللجنة</th>
                        <th class="col-authority">جهة الإصدار</th>
                        <th class="col-file">رقم الاضبارة</th>
                        <th class="col-progress">تقدم الإجراءات</th>
                        <th class="col-violation">نوع المخالفة</th>
                        <th class="col-accused">رتبة المتهم</th>
                        <th class="col-fate">مصير المجلس</th>
                        <th class="col-location">محل التحقيق</th>
                        <th class="col-subject">الموضوع</th>
                        <th class="col-status">الحالة</th>
                        <th class="col-end-date">تاريخ الانتهاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for committee in committees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td class="col-order">{{ committee.order_number }}</td>
                        <td class="col-date">{{ committee.order_date }}</td>
                        <td class="col-type">{{ committee.get_type_display }}</td>
                        <td class="col-chairman-name">{{ committee.chairman_name|default:"-" }}</td>
                        <td class="col-chairman-rank">{{ committee.chairman_rank|default:"-" }}</td>
                        <td class="col-authority">{{ committee.get_issuing_authority_display|default:"-" }}</td>
                        <td class="col-file">{{ committee.file_number|default:"-" }}</td>
                        <td class="col-progress">
                            {% if committee.progress_status %}
                                {% if committee.progress_status == 'completed' %}
                                <span class="badge badge-success">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'not_completed' %}
                                <span class="badge badge-danger">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'under_investigation' %}
                                <span class="badge badge-warning">{{ committee.get_progress_status_display }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ committee.get_progress_status_display }}</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="col-violation">{{ committee.violation_type|default:"-" }}</td>
                        <td class="col-accused">{{ committee.accused_rank|default:"-" }}</td>
                        <td class="col-fate">{{ committee.get_council_fate_display|default:"-" }}</td>
                        <td class="col-location">{{ committee.get_investigation_location_display|default:"-" }}</td>
                        <td class="col-subject">{{ committee.last_action|truncatechars:50|default:"-" }}</td>
                        <td class="col-status">
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td class="col-end-date">
                            {{ committee.end_date }}
                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'committees:detail' committee.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'committees:update' committee.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.user_type == 'admin' %}
                                <a href="{% url 'committees:delete' committee.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="16" class="text-center">لا توجد لجان مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- شريط التنقل -->
{% if page_obj.has_other_pages %}
<div class="card mt-3">
    <div class="card-body py-2">
        <div class="row align-items-center">
            <!-- أزرار التنقل -->
            <div class="col-md-8">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination pagination-sm mb-0 justify-content-center">
                        <!-- السابق -->
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}&per_page={{ per_page }}">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i> السابق</span>
                            </li>
                        {% endif %}

                        <!-- أرقام الصفحات -->
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}&per_page={{ per_page }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        <!-- التالي -->
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}&per_page={{ per_page }}">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">التالي <i class="fas fa-chevron-left"></i></span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>

            <!-- الانتقال السريع -->
            <div class="col-md-4 text-right">
                <div class="d-flex align-items-center justify-content-end">
                    <label for="goto_page" class="mb-0 mr-2">انتقال إلى:</label>
                    <input type="number" id="goto_page" class="form-control form-control-sm" style="width: 60px;"
                           min="1" max="{{ page_obj.paginator.num_pages }}" placeholder="{{ page_obj.number }}">
                    <button type="button" class="btn btn-sm btn-primary ml-2" onclick="gotoPage()">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* منع خروج أي عنصر من الصفحة نهائياً */
    html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    .container {
        overflow-x: hidden !important;
        max-width: 1200px !important;
        width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
        margin: 0 auto !important;
    }

    /* منع أي عنصر من الخروج من viewport */
    * {
        box-sizing: border-box !important;
    }

    /* حماية شاملة من الخروج */
    .main-content, .content-wrapper, .page-content {
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    /* حماية خاصة للجداول */
    .table-container, .table-wrapper {
        overflow-x: hidden !important;
        max-width: 100% !important;
    }
    /* إجبار الجدول على البقاء داخل الإطار نهائياً */
    .table-responsive {
        max-height: 70vh;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: scroll !important; /* مسطرة أفقية إجبارية */
        overflow-y: auto !important; /* مسطرة عمودية */
        position: relative;
        box-sizing: border-box !important;
        display: block !important;
        margin: 0 !important;
    }

    /* تخصيص المسطرة */
    .table-responsive::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #007bff;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #0056b3;
    }

    /* زاوية المسطرة */
    .table-responsive::-webkit-scrollbar-corner {
        background: #f1f1f1;
    }

    #committees-table {
        min-width: 800px; /* عرض أدنى ثابت */
        width: max-content; /* عرض حسب المحتوى */
        max-width: none; /* السماح بالتوسع داخل الحاوي فقط */
        margin: 0;
        border-collapse: collapse;
        table-layout: auto;
        font-size: 14px;
    }

    #committees-table th,
    #committees-table td {
        white-space: nowrap;
        min-width: 100px; /* عرض أدنى أصغر لكل عمود */
    }

    /* عرض مخصص للأعمدة المهمة */
    .col-title {
        min-width: 150px !important;
        max-width: 200px;
    }

    .col-status {
        min-width: 60px !important;
        width: 60px;
    }

    .col-actions {
        min-width: 80px !important;
        width: 80px;
    }

    /* تقليل عرض الأعمدة الأخرى */
    .col-order, .col-date, .col-type, .col-chairman-name, .col-chairman-rank {
        min-width: 70px !important;
        max-width: 120px;
    }

    /* الأعمدة الثابتة - فقط عند وجود مسطرة أفقية */
    .table-responsive.has-horizontal-scroll #committees-table th:first-child,
    .table-responsive.has-horizontal-scroll #committees-table td:first-child {
        position: sticky;
        left: 0;
        background: white;
        z-index: 2;
        width: 50px;
        min-width: 50px;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    .table-responsive.has-horizontal-scroll #committees-table th:last-child,
    .table-responsive.has-horizontal-scroll #committees-table td:last-child {
        position: sticky;
        right: 0;
        background: white;
        z-index: 2;
        width: 120px;
        min-width: 120px;
        box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    }

    /* رؤوس الأعمدة الثابتة */
    .table-responsive.has-horizontal-scroll #committees-table thead th:first-child {
        background-color: #007bff;
        z-index: 12;
    }

    .table-responsive.has-horizontal-scroll #committees-table thead th:last-child {
        background-color: #007bff;
        z-index: 12;
    }

    /* رؤوس الأعمدة */
    #committees-table thead th {
        background-color: #007bff;
        color: white;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* خلايا الجدول */
    #committees-table th,
    #committees-table td {
        padding: 8px 10px;
        vertical-align: middle;
        font-size: 14px;
        line-height: 1.4;
    }

    /* الشارات */
    .badge {
        font-size: 11px;
    }

    /* تحسين عرض الأزرار */
    .btn-group .btn {
        margin: 1px;
        padding: 4px 8px;
        font-size: 12px;
        line-height: 1.4;
    }

    .btn-sm {
        padding: 4px 6px;
        font-size: 11px;
    }



    /* إخفاء الأعمدة الإضافية بشكل افتراضي */
    .col-authority,
    .col-file,
    .col-progress,
    .col-violation,
    .col-accused,
    .col-fate,
    .col-location,
    .col-subject {
        display: none;
    }

    /* الأعمدة الأساسية تظهر دائماً */
    .col-order,
    .col-date,
    .col-type,
    .col-chairman-name,
    .col-chairman-rank,
    .col-status,
    .col-end-date {
        display: table-cell;
    }

    /* منع الجدول من الخروج من الحاوي نهائياً */
    .card {
        overflow: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    .card-body {
        overflow: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 15px !important;
        box-sizing: border-box !important;
    }

    /* إجبار الجدول على البقاء داخل الحاوي */
    .table-responsive {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: scroll !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch;
        margin: 0 !important;
        position: relative !important;
        box-sizing: border-box !important;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تغيير عدد النتائج المعروضة
    function changePerPage() {
        const perPage = document.getElementById('per_page').value;
        const url = new URL(window.location);
        url.searchParams.set('per_page', perPage);
        url.searchParams.set('page', '1'); // العودة للصفحة الأولى
        window.location.href = url.toString();
    }

    // الانتقال لصفحة معينة
    function gotoPage() {
        const pageNum = document.getElementById('goto_page').value;
        const maxPages = {{ page_obj.paginator.num_pages }};

        if (pageNum && pageNum >= 1 && pageNum <= maxPages) {
            const url = new URL(window.location);
            url.searchParams.set('page', pageNum);
            window.location.href = url.toString();
        } else {
            alert('رقم الصفحة غير صحيح. يجب أن يكون بين 1 و ' + maxPages);
        }
    }

    // السماح بالضغط على Enter في مربع الانتقال السريع
    document.addEventListener('DOMContentLoaded', function() {
        const gotoInput = document.getElementById('goto_page');
        if (gotoInput) {
            gotoInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    gotoPage();
                }
            });
        }
    });

    // تبديل عرض الفلاتر المتقدمة
    function toggleAdvancedFilters() {
        const filtersDiv = document.getElementById('advanced-filters');
        if (filtersDiv.style.display === 'none') {
            filtersDiv.style.display = 'block';
            // تمرير سلس
            filtersDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        } else {
            filtersDiv.style.display = 'none';
        }
    }

    // إظهار الفلاتر المتقدمة إذا كانت هناك فلاتر مطبقة
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const hasAdvancedFilters = urlParams.has('type') || urlParams.has('status') ||
                                  urlParams.has('issuing_authority') || urlParams.has('date_from') ||
                                  urlParams.has('date_to') || urlParams.has('end_date_from') ||
                                  urlParams.has('end_date_to');

        if (hasAdvancedFilters) {
            document.getElementById('advanced-filters').style.display = 'block';
        }
    });

    // تصدير مع الأعمدة المرئية فقط
    function exportWithVisibleColumns() {
        // الحصول على الأعمدة المرئية
        const visibleColumns = [];
        const checkboxes = document.querySelectorAll('.column-toggle');

        checkboxes.forEach(function(checkbox) {
            if (checkbox.checked) {
                // استخراج اسم العمود من id
                const columnName = checkbox.id.replace('col-', '');
                visibleColumns.push(columnName);
            }
        });

        // إنشاء URL للتصدير مع الأعمدة المرئية
        const currentUrl = new URL(window.location);
        const exportUrl = new URL('{% url "committees:export_excel" %}', window.location.origin);

        // نسخ جميع معاملات البحث والفلاتر
        currentUrl.searchParams.forEach((value, key) => {
            exportUrl.searchParams.set(key, value);
        });

        // إضافة الأعمدة المرئية
        if (visibleColumns.length > 0) {
            exportUrl.searchParams.set('visible_columns', visibleColumns.join(','));
        }

        // الانتقال لرابط التصدير
        window.location.href = exportUrl.toString();
    }
</script>

<script>
$(document).ready(function() {
    // تحميل حالة الأعمدة المحفوظة
    loadColumnState();

    // إظهار/إخفاء قائمة التحكم في الأعمدة
    $('#toggle-columns').click(function() {
        $('#columns-control').toggle();
    });

    // التحكم في إظهار/إخفاء الأعمدة
    $('.column-toggle').change(function() {
        var columnClass = $(this).attr('id');
        var isChecked = $(this).is(':checked');

        if (isChecked) {
            $('.' + columnClass).css('display', 'table-cell');
        } else {
            $('.' + columnClass).css('display', 'none');
        }

        // تحديث عرض الجدول بناءً على عدد الأعمدة المرئية
        updateTableWidth();

        // حفظ الحالة
        saveColumnState();
    });

    // دالة حفظ حالة الأعمدة
    function saveColumnState() {
        var columnStates = {};
        $('.column-toggle').each(function() {
            columnStates[$(this).attr('id')] = $(this).is(':checked');
        });
        localStorage.setItem('committeeColumnsState', JSON.stringify(columnStates));
    }

    // دالة تحديث عرض الجدول
    function updateTableWidth() {
        // عد الأعمدة المرئية الإجمالية
        var visibleColumns = 0;
        $('.column-toggle').each(function() {
            if ($(this).is(':checked')) {
                visibleColumns++;
            }
        });

        // إضافة الأعمدة الأساسية (الرقم والإجراءات)
        var totalColumns = visibleColumns + 2; // +2 للرقم والإجراءات

        // تحديد العرض بناءً على عدد الأعمدة
        if (totalColumns > 7) {
            // إذا كان هناك أكثر من 7 أعمدة، زيادة عرض الجدول
            var minWidth = totalColumns * 130; // 130px لكل عمود
            $('#committees-table').css({
                'min-width': minWidth + 'px',
                'width': 'max-content'
            });

            // تفعيل المسطرة الأفقية
            $('.table-responsive').addClass('has-horizontal-scroll');
        } else {
            // إذا كان 7 أعمدة أو أقل، استخدم العرض الطبيعي
            $('#committees-table').css({
                'min-width': '800px',
                'width': '100%'
            });
            $('.table-responsive').removeClass('has-horizontal-scroll');
        }

        // إجبار إعادة حساب العرض
        $('.table-responsive').trigger('scroll');
    }



    // دالة تحميل حالة الأعمدة
    function loadColumnState() {
        var savedStates = localStorage.getItem('committeeColumnsState');
        if (savedStates) {
            var columnStates = JSON.parse(savedStates);
            $.each(columnStates, function(columnId, isVisible) {
                $('#' + columnId).prop('checked', isVisible);
                if (isVisible) {
                    $('.' + columnId).css('display', 'table-cell');
                } else {
                    $('.' + columnId).css('display', 'none');
                }
            });
        }
    }

    // تحديث عرض الجدول عند تحميل الصفحة
    updateTableWidth();
});
</script>
{% endblock %}
