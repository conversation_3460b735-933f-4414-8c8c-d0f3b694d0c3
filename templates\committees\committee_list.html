{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}قائمة اللجان{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>قائمة اللجان</h1>
    <div>
        <button class="btn btn-secondary" id="toggle-columns">
            <i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة
        </button>
        <a href="{% url 'committees:export_excel' %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </a>
        {% if user.user_type == 'admin' %}
        <a href="{% url 'committees:import_excel' %}" class="btn btn-info">
            <i class="fas fa-file-upload"></i> استيراد Excel
        </a>
        {% endif %}
        <a href="{% url 'committees:create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة لجنة جديدة
        </a>
    </div>
</div>

<!-- قائمة إظهار/إخفاء الأعمدة -->
<div class="card mb-3" id="columns-control" style="display: none;">
    <div class="card-body">
        <h6>إظهار/إخفاء الأعمدة:</h6>
        <div class="row">
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-title" checked>
                    <label class="form-check-label" for="col-title">العنوان</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-order" checked>
                    <label class="form-check-label" for="col-order">رقم الأمر</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-date" checked>
                    <label class="form-check-label" for="col-date">تاريخ التشكيل</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-type" checked>
                    <label class="form-check-label" for="col-type">النوع</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman" checked>
                    <label class="form-check-label" for="col-chairman">رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-authority" checked>
                    <label class="form-check-label" for="col-authority">جهة الإصدار</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-file" checked>
                    <label class="form-check-label" for="col-file">رقم الاضبارة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-progress" checked>
                    <label class="form-check-label" for="col-progress">تقدم الإجراءات</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-violation" checked>
                    <label class="form-check-label" for="col-violation">نوع المخالفة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-accused" checked>
                    <label class="form-check-label" for="col-accused">رتبة المتهم</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-fate" checked>
                    <label class="form-check-label" for="col-fate">مصير المجلس</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-location" checked>
                    <label class="form-check-label" for="col-location">محل التحقيق</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-status" checked>
                    <label class="form-check-label" for="col-status">الحالة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-end-date" checked>
                    <label class="form-check-label" for="col-end-date">تاريخ الانتهاء</label>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">اللجان والمجالس</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i>
            <strong>تلميح:</strong> يمكنك التمرير أفقياً لرؤية جميع الأعمدة، أو استخدم زر "إظهار/إخفاء الأعمدة" لتخصيص العرض.
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="table-container">
            <table class="table table-striped table-hover" id="committees-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th class="col-title">العنوان</th>
                        <th class="col-order">رقم الأمر</th>
                        <th class="col-date">تاريخ التشكيل</th>
                        <th class="col-type">النوع</th>
                        <th class="col-chairman">رئيس اللجنة</th>
                        <th class="col-authority">جهة الإصدار</th>
                        <th class="col-file">رقم الاضبارة</th>
                        <th class="col-progress">تقدم الإجراءات</th>
                        <th class="col-violation">نوع المخالفة</th>
                        <th class="col-accused">رتبة المتهم</th>
                        <th class="col-fate">مصير المجلس</th>
                        <th class="col-location">محل التحقيق</th>
                        <th class="col-status">الحالة</th>
                        <th class="col-end-date">تاريخ الانتهاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for committee in committees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td class="col-title">{{ committee.title }}</td>
                        <td class="col-order">{{ committee.order_number }}</td>
                        <td class="col-date">{{ committee.order_date }}</td>
                        <td class="col-type">{{ committee.get_type_display }}</td>
                        <td class="col-chairman">
                            {% if committee.chairman_rank %}{{ committee.chairman_rank }}{% endif %}
                            {% if committee.chairman_name %}<br>{{ committee.chairman_name }}{% endif %}
                        </td>
                        <td class="col-authority">{{ committee.get_issuing_authority_display|default:"-" }}</td>
                        <td class="col-file">{{ committee.file_number|default:"-" }}</td>
                        <td class="col-progress">
                            {% if committee.progress_status %}
                                {% if committee.progress_status == 'completed' %}
                                <span class="badge badge-success">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'not_completed' %}
                                <span class="badge badge-danger">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'under_investigation' %}
                                <span class="badge badge-warning">{{ committee.get_progress_status_display }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ committee.get_progress_status_display }}</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="col-violation">{{ committee.violation_type|default:"-" }}</td>
                        <td class="col-accused">{{ committee.accused_rank|default:"-" }}</td>
                        <td class="col-fate">{{ committee.get_council_fate_display|default:"-" }}</td>
                        <td class="col-location">{{ committee.get_investigation_location_display|default:"-" }}</td>
                        <td class="col-status">
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td class="col-end-date">
                            {{ committee.end_date }}
                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'committees:detail' committee.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'committees:update' committee.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.user_type == 'admin' %}
                                <a href="{% url 'committees:delete' committee.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="16" class="text-center">لا توجد لجان مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* حاوي الجدول مع scroll أفقي قوي */
    .table-container {
        width: 100%;
        overflow-x: scroll !important;
        overflow-y: auto;
        border: 2px solid #007bff;
        border-radius: 8px;
        background: white;
        position: relative;
        max-height: 80vh;
        /* تأكيد ظهور الـ scrollbar */
        scrollbar-width: thick;
        scrollbar-color: #007bff #f1f1f1;
    }

    /* تخصيص الـ scrollbar للمتصفحات المبنية على Webkit */
    .table-container::-webkit-scrollbar {
        height: 15px;
        width: 15px;
    }

    .table-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .table-container::-webkit-scrollbar-thumb {
        background: #007bff;
        border-radius: 10px;
        border: 2px solid #f1f1f1;
    }

    .table-container::-webkit-scrollbar-thumb:hover {
        background: #0056b3;
    }

    /* الجدول بعرض ثابت كبير */
    #committees-table {
        width: 2500px !important;
        min-width: 2500px !important;
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    /* خلايا الجدول */
    #committees-table th,
    #committees-table td {
        white-space: nowrap !important;
        padding: 12px 15px;
        border: 1px solid #dee2e6;
        min-width: 150px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* العمود الأول (الرقم) */
    #committees-table th:first-child,
    #committees-table td:first-child {
        min-width: 60px !important;
        max-width: 60px !important;
        position: sticky;
        left: 0;
        background-color: #f8f9fa;
        z-index: 15;
        border-right: 2px solid #007bff;
    }

    /* عمود الإجراءات */
    #committees-table th:last-child,
    #committees-table td:last-child {
        min-width: 180px !important;
        position: sticky;
        right: 0;
        background-color: #f8f9fa;
        z-index: 15;
        border-left: 2px solid #007bff;
    }

    /* رؤوس الأعمدة */
    #committees-table thead th {
        background-color: #007bff !important;
        color: white !important;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 20;
    }

    /* رأس العمود الأول */
    #committees-table thead th:first-child {
        background-color: #0056b3 !important;
        z-index: 25;
    }

    /* رأس عمود الإجراءات */
    #committees-table thead th:last-child {
        background-color: #0056b3 !important;
        z-index: 25;
    }

    /* أعمدة محددة */
    .col-title {
        min-width: 250px !important;
        max-width: 300px !important;
    }

    .col-chairman {
        min-width: 200px !important;
    }

    .col-violation,
    .col-accused {
        min-width: 180px !important;
    }

    /* الشارات */
    .badge {
        white-space: nowrap !important;
        font-size: 11px;
    }

    /* تحسينات عامة */
    .card-body {
        padding: 20px;
    }

    /* مؤشر الـ scroll */
    .table-container::before {
        content: "⟵ مرر يميناً ويساراً لرؤية جميع الأعمدة ⟶";
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: #28a745;
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        z-index: 30;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    /* إخفاء المؤشر بعد فترة */
    .table-container.scrolled::before {
        display: none;
    }
</style>

    #committees-table thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 20;
        border-bottom: 2px solid #dee2e6;
    }

    /* تحسين عرض الأعمدة الطويلة */
    .col-title {
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .col-violation,
    .col-accused {
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* تحسين عرض الأزرار */
    .btn-group .btn {
        margin: 1px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // إظهار/إخفاء قائمة التحكم في الأعمدة
    $('#toggle-columns').click(function() {
        $('#columns-control').toggle();
    });

    // التحكم في إظهار/إخفاء الأعمدة
    $('.column-toggle').change(function() {
        var columnClass = $(this).attr('id');
        var isChecked = $(this).is(':checked');

        if (isChecked) {
            $('.' + columnClass).show();
        } else {
            $('.' + columnClass).hide();
        }

        // إعادة حساب عرض الجدول
        updateTableWidth();
    });

    // وظيفة لإعادة حساب عرض الجدول
    function updateTableWidth() {
        var visibleColumns = 0;
        $('.column-toggle').each(function() {
            if ($(this).is(':checked')) {
                visibleColumns++;
            }
        });

        // عرض أساسي + عرض الأعمدة المرئية
        var tableWidth = 500 + (visibleColumns * 150);
        $('#committees-table').css('width', tableWidth + 'px');
        $('#committees-table').css('min-width', tableWidth + 'px');
    }

    // إضافة مؤشرات للـ scroll
    $('.table-container').on('scroll', function() {
        $(this).addClass('scrolled');
    });

    // إظهار مؤشر الـ scroll لفترة محددة
    setTimeout(function() {
        $('.table-container').addClass('scrolled');
    }, 5000);

    // حفظ حالة الأعمدة في localStorage
    $('.column-toggle').change(function() {
        var columnStates = {};
        $('.column-toggle').each(function() {
            columnStates[$(this).attr('id')] = $(this).is(':checked');
        });
        localStorage.setItem('committeeColumnsState', JSON.stringify(columnStates));
    });

    // استرجاع حالة الأعمدة من localStorage
    var savedStates = localStorage.getItem('committeeColumnsState');
    if (savedStates) {
        var columnStates = JSON.parse(savedStates);
        $.each(columnStates, function(columnId, isVisible) {
            $('#' + columnId).prop('checked', isVisible);
            if (isVisible) {
                $('.' + columnId).show();
            } else {
                $('.' + columnId).hide();
            }
        });
        updateTableWidth();
    }

    // تحديث عرض الجدول عند تحميل الصفحة
    updateTableWidth();

    // تحسين تجربة المستخدم
    $(window).resize(function() {
        updateTableWidth();
    });
});
</script>
{% endblock %}
