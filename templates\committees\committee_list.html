{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}قائمة اللجان{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>قائمة اللجان</h1>
    <div>
        <button class="btn btn-secondary" id="toggle-columns">
            <i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة
        </button>
        <a href="{% url 'committees:export_excel' %}" class="btn btn-success">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </a>
        {% if user.user_type == 'admin' %}
        <a href="{% url 'committees:import_excel' %}" class="btn btn-info">
            <i class="fas fa-file-upload"></i> استيراد Excel
        </a>
        {% endif %}
        <a href="{% url 'committees:create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة لجنة جديدة
        </a>
    </div>
</div>

<!-- قائمة إظهار/إخفاء الأعمدة -->
<div class="card mb-3" id="columns-control" style="display: none;">
    <div class="card-body">
        <h6>إظهار/إخفاء الأعمدة:</h6>
        <div class="row">
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-title" checked>
                    <label class="form-check-label" for="col-title">العنوان</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-order" checked>
                    <label class="form-check-label" for="col-order">رقم الأمر</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-date" checked>
                    <label class="form-check-label" for="col-date">تاريخ التشكيل</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-type" checked>
                    <label class="form-check-label" for="col-type">النوع</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-chairman" checked>
                    <label class="form-check-label" for="col-chairman">رئيس اللجنة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-authority" checked>
                    <label class="form-check-label" for="col-authority">جهة الإصدار</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-file" checked>
                    <label class="form-check-label" for="col-file">رقم الاضبارة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-progress" checked>
                    <label class="form-check-label" for="col-progress">تقدم الإجراءات</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-violation" checked>
                    <label class="form-check-label" for="col-violation">نوع المخالفة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-accused" checked>
                    <label class="form-check-label" for="col-accused">رتبة المتهم</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-fate" checked>
                    <label class="form-check-label" for="col-fate">مصير المجلس</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-location" checked>
                    <label class="form-check-label" for="col-location">محل التحقيق</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-status" checked>
                    <label class="form-check-label" for="col-status">الحالة</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input column-toggle" type="checkbox" id="col-end-date" checked>
                    <label class="form-check-label" for="col-end-date">تاريخ الانتهاء</label>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">اللجان والمجالس</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover w-100" id="committees-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th class="col-title">العنوان</th>
                        <th class="col-order">رقم الأمر</th>
                        <th class="col-date">تاريخ التشكيل</th>
                        <th class="col-type">النوع</th>
                        <th class="col-chairman">رئيس اللجنة</th>
                        <th class="col-authority">جهة الإصدار</th>
                        <th class="col-file">رقم الاضبارة</th>
                        <th class="col-progress">تقدم الإجراءات</th>
                        <th class="col-violation">نوع المخالفة</th>
                        <th class="col-accused">رتبة المتهم</th>
                        <th class="col-fate">مصير المجلس</th>
                        <th class="col-location">محل التحقيق</th>
                        <th class="col-status">الحالة</th>
                        <th class="col-end-date">تاريخ الانتهاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for committee in committees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td class="col-title">{{ committee.title }}</td>
                        <td class="col-order">{{ committee.order_number }}</td>
                        <td class="col-date">{{ committee.order_date }}</td>
                        <td class="col-type">{{ committee.get_type_display }}</td>
                        <td class="col-chairman">
                            {% if committee.chairman_rank %}{{ committee.chairman_rank }}{% endif %}
                            {% if committee.chairman_name %}<br>{{ committee.chairman_name }}{% endif %}
                        </td>
                        <td class="col-authority">{{ committee.get_issuing_authority_display|default:"-" }}</td>
                        <td class="col-file">{{ committee.file_number|default:"-" }}</td>
                        <td class="col-progress">
                            {% if committee.progress_status %}
                                {% if committee.progress_status == 'completed' %}
                                <span class="badge badge-success">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'not_completed' %}
                                <span class="badge badge-danger">{{ committee.get_progress_status_display }}</span>
                                {% elif committee.progress_status == 'under_investigation' %}
                                <span class="badge badge-warning">{{ committee.get_progress_status_display }}</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ committee.get_progress_status_display }}</span>
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="col-violation">{{ committee.violation_type|default:"-" }}</td>
                        <td class="col-accused">{{ committee.accused_rank|default:"-" }}</td>
                        <td class="col-fate">{{ committee.get_council_fate_display|default:"-" }}</td>
                        <td class="col-location">{{ committee.get_investigation_location_display|default:"-" }}</td>
                        <td class="col-status">
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td class="col-end-date">
                            {{ committee.end_date }}
                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'committees:detail' committee.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'committees:update' committee.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.user_type == 'admin' %}
                                <a href="{% url 'committees:delete' committee.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="16" class="text-center">لا توجد لجان مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات عامة للجدول */
    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        max-height: 70vh;
        overflow: auto; /* مسطرة أفقية وعمودية */
        position: relative;
    }

    /* تخصيص المسطرة */
    .table-responsive::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #007bff;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #0056b3;
    }

    /* زاوية المسطرة */
    .table-responsive::-webkit-scrollbar-corner {
        background: #f1f1f1;
    }

    #committees-table {
        min-width: 100%; /* عرض طبيعي افتراضي */
        width: 100%;
    }

    #committees-table th,
    #committees-table td {
        white-space: nowrap;
        min-width: 100px; /* عرض أدنى أصغر لكل عمود */
    }

    /* عرض مخصص للأعمدة المهمة */
    .col-title {
        min-width: 200px !important;
        max-width: 300px;
    }

    .col-status {
        min-width: 80px !important;
        width: 80px;
    }

    .col-actions {
        min-width: 120px !important;
        width: 120px;
    }

    /* الأعمدة الثابتة */
    #committees-table th:first-child,
    #committees-table td:first-child {
        position: sticky;
        left: 0;
        background: white;
        z-index: 2;
        width: 50px;
        min-width: 50px;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1); /* ظل للفصل البصري */
    }

    #committees-table th:last-child,
    #committees-table td:last-child {
        position: sticky;
        right: 0;
        background: white;
        z-index: 2;
        width: 120px;
        min-width: 120px;
        box-shadow: -2px 0 5px rgba(0,0,0,0.1); /* ظل للفصل البصري */
    }

    /* رؤوس الأعمدة الثابتة */
    #committees-table thead th:first-child {
        background-color: #007bff;
        z-index: 12;
    }

    #committees-table thead th:last-child {
        background-color: #007bff;
        z-index: 12;
    }

    /* رؤوس الأعمدة */
    #committees-table thead th {
        background-color: #007bff;
        color: white;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    /* خلايا الجدول */
    #committees-table th,
    #committees-table td {
        padding: 8px 12px;
        vertical-align: middle;
    }

    /* الشارات */
    .badge {
        font-size: 11px;
    }

    /* تحسين عرض الأزرار */
    .btn-group .btn {
        margin: 1px;
    }

    /* تنبيه بصري للمسطرة الأفقية */
    .table-responsive.has-horizontal-scroll::before {
        content: "← تمرر أفقياً لرؤية المزيد →";
        position: absolute;
        top: 5px;
        right: 50%;
        transform: translateX(50%);
        background: rgba(0, 123, 255, 0.9);
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 11px;
        z-index: 100;
        animation: fadeInOut 3s ease-in-out;
    }

    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }

    /* إخفاء الأعمدة بشكل افتراضي */
    .col-authority,
    .col-file,
    .col-progress,
    .col-violation,
    .col-accused,
    .col-fate,
    .col-location {
        display: none;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // إظهار/إخفاء قائمة التحكم في الأعمدة
    $('#toggle-columns').click(function() {
        $('#columns-control').toggle();
    });

    // التحكم في إظهار/إخفاء الأعمدة
    $('.column-toggle').change(function() {
        var columnClass = $(this).attr('id');
        var isChecked = $(this).is(':checked');

        if (isChecked) {
            $('.' + columnClass).show();
        } else {
            $('.' + columnClass).hide();
        }

        // تحديث عرض الجدول بناءً على عدد الأعمدة المرئية
        updateTableWidth();
    });

    // دالة تحديث عرض الجدول
    function updateTableWidth() {
        var visibleColumns = 0;
        $('.column-toggle').each(function() {
            if ($(this).is(':checked')) {
                visibleColumns++;
            }
        });

        // إذا كان هناك أعمدة إضافية مرئية، قم بزيادة العرض
        if (visibleColumns > 0) {
            // الأعمدة الأساسية (العنوان، الحالة، الإجراءات، إلخ)
            var baseColumns = 5;
            var totalColumns = baseColumns + visibleColumns;

            // تحديد العرض الأدنى بناءً على عدد الأعمدة (أكثر تحفظاً)
            var minWidth = Math.max(800, totalColumns * 120);
            $('#committees-table').css('min-width', minWidth + 'px');

            // إضافة تنبيه بصري عند وجود مسطرة أفقية
            setTimeout(function() {
                if ($('#committees-table').width() > $('.table-responsive').width()) {
                    $('.table-responsive').addClass('has-horizontal-scroll');
                } else {
                    $('.table-responsive').removeClass('has-horizontal-scroll');
                }
            }, 100);
        } else {
            // إذا لم تكن هناك أعمدة إضافية، استخدم العرض الطبيعي
            $('#committees-table').css('min-width', '100%');
            $('.table-responsive').removeClass('has-horizontal-scroll');
        }
    }

    // حفظ حالة الأعمدة في localStorage
    $('.column-toggle').change(function() {
        var columnStates = {};
        $('.column-toggle').each(function() {
            columnStates[$(this).attr('id')] = $(this).is(':checked');
        });
        localStorage.setItem('committeeColumnsState', JSON.stringify(columnStates));
    });

    // استرجاع حالة الأعمدة من localStorage
    var savedStates = localStorage.getItem('committeeColumnsState');
    if (savedStates) {
        var columnStates = JSON.parse(savedStates);
        $.each(columnStates, function(columnId, isVisible) {
            $('#' + columnId).prop('checked', isVisible);
            if (isVisible) {
                $('.' + columnId).show();
            } else {
                $('.' + columnId).hide();
            }
        });
    }

    // تحديث عرض الجدول عند تحميل الصفحة
    updateTableWidth();
});
</script>
{% endblock %}
