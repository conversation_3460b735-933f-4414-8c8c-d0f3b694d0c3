#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي ناقص الأعمدة لاختبار الاستيراد المرن
"""

from openpyxl import Workbook

def create_partial_excel():
    """إنشاء ملف Excel تجريبي بأعمدة ناقصة"""
    wb = Workbook()
    ws = wb.active
    ws.title = "اللجان والمجالس"

    # تعيين العناوين (فقط الأعمدة الأساسية - 7 أعمدة من أصل 19)
    headers = [
        'العنوان', 'رقم الأمر الإداري', 'تاريخ التشكيل', 'النوع', 'الجهة المسؤولة',
        'المدة (بالأيام)', 'تاريخ الانتهاء'
    ]

    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)

    # إضافة بيانات تجريبية
    test_data = [
        [
            'لجنة التحقيق في المخالفات المالية',  # العنوان
            'أ.إ/2024/001',  # رقم الأمر الإداري
            '2024-01-15',  # تاريخ التشكيل
            'لجنة تحقيقية',  # النوع
            'الإدارة القانونية',  # الجهة المسؤولة
            60,  # المدة (بالأيام)
            '2024-03-15',  # تاريخ الانتهاء
        ],
        [
            'مجلس تحقيقي للنظر في قضية الإهمال',
            'م.ت/2024/002',
            '2024-02-01',
            'مجلس تحقيقي',
            'إدارة الشؤون الإدارية',
            45,
            '2024-03-17',
        ],
        [
            'لجنة مشتركة لمراجعة الإجراءات',
            'ل.م/2024/003',
            '2024-02-10',
            'لجنة مشتركة',
            'الإدارة العامة',
            30,
            '2024-03-11',
        ]
    ]

    # كتابة البيانات
    for row_num, row_data in enumerate(test_data, 2):
        for col_num, value in enumerate(row_data, 1):
            ws.cell(row=row_num, column=col_num, value=value)

    # حفظ الملف
    filename = 'test_partial_committees.xlsx'
    wb.save(filename)
    print(f'تم إنشاء ملف Excel ناقص الأعمدة: {filename}')
    print(f'الملف يحتوي على {len(headers)} أعمدة من أصل 19 عمود متوقع')
    print('هذا الملف مناسب لاختبار الاستيراد المرن')

if __name__ == '__main__':
    create_partial_excel()
