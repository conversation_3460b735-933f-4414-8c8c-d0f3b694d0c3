from django.urls import path
from . import views

app_name = 'departments'

urlpatterns = [
    # إدارة الأقسام
    path('', views.department_list, name='list'),
    path('create/', views.department_create, name='create'),
    path('<int:pk>/', views.department_detail, name='detail'),
    path('<int:pk>/update/', views.department_update, name='update'),
    path('<int:pk>/delete/', views.department_delete, name='delete'),

    # إدارة مجموعات الحقول
    path('<int:pk>/add-group/', views.add_field_group, name='add_field_group'),
    path('groups/<int:pk>/update/', views.update_field_group, name='update_field_group'),
    path('groups/<int:pk>/delete/', views.delete_field_group, name='delete_field_group'),

    # إدارة الحقول
    path('<int:pk>/add-field/', views.add_field, name='add_field'),
    path('<int:pk>/add-field-to-group/<int:group_id>/', views.add_field_to_group, name='add_field_to_group'),
    path('fields/<int:pk>/update/', views.update_field, name='update_field'),
    path('fields/<int:pk>/delete/', views.delete_field, name='delete_field'),

    # إدارة البيانات
    path('<int:pk>/data/', views.department_data_list, name='data_list'),
    path('<int:pk>/data/create/', views.department_data_create, name='data_create'),
    path('data/<int:pk>/', views.department_data_detail, name='data_detail'),
    path('data/<int:pk>/update/', views.department_data_update, name='data_update'),
    path('data/<int:pk>/delete/', views.department_data_delete, name='data_delete'),

    # تصدير واستيراد البيانات
    path('<int:pk>/export/', views.department_data_export, name='data_export'),
    path('<int:pk>/import/', views.department_data_import, name='data_import'),
    path('<int:pk>/report/', views.department_data_report, name='data_report'),

    # واجهة السحب والإفلات
    path('<int:pk>/designer/', views.department_designer, name='designer'),
    path('<int:pk>/reorder-fields/', views.reorder_fields, name='reorder_fields'),

    # واجهة API
    path('api/fields/<int:pk>/options/', views.field_options, name='field_options'),
]
