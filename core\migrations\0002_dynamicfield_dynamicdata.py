# Generated by Django 5.2 on 2025-04-19 09:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('section', models.CharField(choices=[('committees', 'اللجان والمجالس'), ('guarantees', 'الكفالات والاستشارات')], max_length=50, verbose_name='القسم')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحقل البرمجي')),
                ('display_name', models.CharField(max_length=100, verbose_name='اسم العرض')),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('select', 'قائمة منسدلة'), ('textarea', 'نص طويل'), ('file', 'ملف'), ('boolean', 'نعم/لا')], max_length=20, verbose_name='نوع الحقل')),
                ('required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('options', models.TextField(blank=True, help_text='للقوائم المنسدلة فقط، بتنسيق JSON', null=True, verbose_name='الخيارات')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, help_text='الحقول الافتراضية لا يمكن حذفها', verbose_name='حقل افتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_fields', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'حقل ديناميكي',
                'verbose_name_plural': 'الحقول الديناميكية',
                'ordering': ['section', 'order'],
                'unique_together': {('section', 'name')},
            },
        ),
        migrations.CreateModel(
            name='DynamicData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField()),
                ('text_value', models.TextField(blank=True, null=True, verbose_name='قيمة نصية')),
                ('number_value', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='قيمة رقمية')),
                ('date_value', models.DateField(blank=True, null=True, verbose_name='قيمة تاريخ')),
                ('boolean_value', models.BooleanField(blank=True, null=True, verbose_name='قيمة منطقية')),
                ('file_value', models.FileField(blank=True, null=True, upload_to='dynamic_files/%Y/%m/%d/', verbose_name='قيمة ملف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='data', to='core.dynamicfield', verbose_name='الحقل')),
            ],
            options={
                'verbose_name': 'بيانات ديناميكية',
                'verbose_name_plural': 'بيانات ديناميكية',
                'unique_together': {('field', 'content_type', 'object_id')},
            },
        ),
    ]
