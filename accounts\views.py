from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login as auth_login, authenticate, update_session_auth_hash, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib import messages
from .models import User
from .forms import UserForm, UserUpdateForm, ProfileForm

def login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        if user is not None:
            auth_login(request, user)
            return redirect('dashboard')
        else:
            messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة.')
    return render(request, 'accounts/login.html')

def logout_view(request):
    if request.method == 'POST':
        logout(request)
        messages.success(request, 'تم تسجيل الخروج بنجاح.')
        return redirect('login')
    return render(request, 'accounts/logout.html')

@login_required
def profile(request):
    if request.method == 'POST':
        form = ProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الملف الشخصي بنجاح.')
            return redirect('accounts:profile')
    else:
        form = ProfileForm(instance=request.user)

    return render(request, 'accounts/profile.html', {'form': form})

@login_required
def change_password(request):
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)
            messages.success(request, 'تم تغيير كلمة المرور بنجاح.')
            return redirect('accounts:profile')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'accounts/change_password.html', {'form': form})

@login_required
def user_list(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    users = User.objects.all()
    return render(request, 'accounts/user_list.html', {'users': users})

@login_required
def user_detail(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    user = get_object_or_404(User, pk=pk)
    return render(request, 'accounts/user_detail.html', {'user': user})

@login_required
def user_create(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = UserForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, 'تم إنشاء المستخدم بنجاح.')
            return redirect('accounts:user_list')
    else:
        form = UserForm()

    return render(request, 'accounts/user_form.html', {'form': form})

@login_required
def user_update(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    user = get_object_or_404(User, pk=pk)

    if request.method == 'POST':
        form = UserUpdateForm(request.POST, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث المستخدم بنجاح.')
            return redirect('accounts:user_list')
    else:
        form = UserUpdateForm(instance=user)

    return render(request, 'accounts/user_form.html', {'form': form, 'user': user})

@login_required
def user_delete(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    user = get_object_or_404(User, pk=pk)

    if request.user == user:
        messages.error(request, 'لا يمكنك حذف حسابك الخاص.')
        return redirect('accounts:user_list')

    if request.method == 'POST':
        user.delete()
        messages.success(request, 'تم حذف المستخدم بنجاح.')
        return redirect('accounts:user_list')

    return render(request, 'accounts/user_confirm_delete.html', {'user': user})
