from django.contrib import admin
from .models import Committee, CommitteeMember

class CommitteeMemberInline(admin.TabularInline):
    model = CommitteeMember
    extra = 1

@admin.register(Committee)
class CommitteeAdmin(admin.ModelAdmin):
    list_display = ('title', 'order_number', 'order_date', 'type', 'responsible_entity', 'status', 'end_date', 'created_by')
    list_filter = ('type', 'status', 'created_by')
    search_fields = ('title', 'order_number', 'responsible_entity', 'last_action', 'notes')
    date_hierarchy = 'order_date'
    inlines = [CommitteeMemberInline]

@admin.register(CommitteeMember)
class CommitteeMemberAdmin(admin.ModelAdmin):
    list_display = ('name', 'committee', 'role')
    list_filter = ('committee', 'role')
    search_fields = ('name',)
