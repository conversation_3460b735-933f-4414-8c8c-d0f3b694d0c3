{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}لوحة التحكم{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.css">
{% endblock %}

{% block content %}
<h1 class="mb-4">لوحة التحكم</h1>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">إحصائيات اللجان</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span>اللجان النشطة:</span>
                    <span class="badge badge-primary">{{ active_committees }}</span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>اللجان المغلقة:</span>
                    <span class="badge badge-secondary">{{ closed_committees }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>اللجان التي ستنتهي قريباً:</span>
                    <span class="badge badge-warning">{{ expiring_committees }}</span>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'committees:list' %}" class="btn btn-sm btn-primary">عرض اللجان</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">إحصائيات الكفالات</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span>الكفالات النشطة:</span>
                    <span class="badge badge-success">{{ active_guarantees }}</span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>الكفالات المنتهية:</span>
                    <span class="badge badge-danger">{{ expired_guarantees }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>الكفالات التي ستنتهي قريباً:</span>
                    <span class="badge badge-warning">{{ expiring_guarantees }}</span>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'guarantees:list' %}" class="btn btn-sm btn-success">عرض الكفالات</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">إحصائيات المستخدمين</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                    <span>مدراء النظام:</span>
                    <span class="badge badge-info">{{ admins }}</span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <span>مدراء الأقسام:</span>
                    <span class="badge badge-info">{{ department_managers }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>مدخلي البيانات:</span>
                    <span class="badge badge-info">{{ data_entries }}</span>
                </div>
            </div>
            <div class="card-footer">
                {% if user.user_type == 'admin' %}
                <a href="{% url 'accounts:user_list' %}" class="btn btn-sm btn-info">إدارة المستخدمين</a>
                {% else %}
                <span class="text-muted">لا تملك صلاحية إدارة المستخدمين</span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">اللجان المضافة خلال العام</h5>
            </div>
            <div class="card-body">
                <canvas id="committeesChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">الكفالات المضافة خلال العام</h5>
            </div>
            <div class="card-body">
                <canvas id="guaranteesChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">آخر الإشعارات</h5>
            </div>
            <div class="card-body">
                {% if notifications %}
                <div class="list-group">
                    {% for notification in notifications %}
                    <div class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ notification.title }}</h5>
                            <small>{{ notification.created_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        <p class="mb-1">{{ notification.message }}</p>
                        <small>{{ notification.get_type_display }}</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-center">لا توجد إشعارات جديدة</p>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{% url 'core:notifications' %}" class="btn btn-sm btn-warning">عرض جميع الإشعارات</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
<script>
    // رسم بياني للجان
    var committeesCtx = document.getElementById('committeesChart').getContext('2d');
    var committeesChart = new Chart(committeesCtx, {
        type: 'line',
        data: {
            labels: {{ committees_by_month.months|safe }},
            datasets: [{
                label: 'عدد اللجان',
                data: {{ committees_by_month.data|safe }},
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
    
    // رسم بياني للكفالات
    var guaranteesCtx = document.getElementById('guaranteesChart').getContext('2d');
    var guaranteesChart = new Chart(guaranteesCtx, {
        type: 'line',
        data: {
            labels: {{ guarantees_by_month.months|safe }},
            datasets: [{
                label: 'عدد الكفالات',
                data: {{ guarantees_by_month.data|safe }},
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            }
        }
    });
</script>
{% endblock %}
