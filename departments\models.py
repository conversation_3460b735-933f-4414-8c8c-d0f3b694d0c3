from django.db import models
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.cache import cache
import json

class Department(models.Model):
    ACCESS_CHOICES = (
        ('all', _('جميع المستخدمين')),
        ('admin', _('المشرفون فقط')),
        ('specific', _('مستخدمون محددون')),
    )

    name = models.CharField(_('اسم القسم'), max_length=100)
    description = models.TextField(_('وصف القسم'), blank=True, null=True)
    icon = models.CharField(_('أيقونة القسم'), max_length=50, blank=True, null=True)
    color = models.CharField(_('لون القسم'), max_length=20, default='#3498db', help_text=_('لون القسم في الواجهة'))
    table_name = models.CharField(_('اسم الجدول'), max_length=100, blank=True, null=True)
    display_name = models.CharField(_('اسم العرض'), max_length=100, blank=True, null=True, help_text=_('اسم العرض في واجهة المستخدم'))
    order = models.PositiveIntegerField(_('الترتيب'), default=0, help_text=_('ترتيب ظهور القسم في القائمة'))
    manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_departments',
        verbose_name=_('مدير القسم')
    )
    access_level = models.CharField(
        _('مستوى الوصول'),
        max_length=20,
        choices=ACCESS_CHOICES,
        default='all',
        help_text=_('تحديد من يمكنه الوصول إلى هذا القسم')
    )
    authorized_users = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='accessible_departments',
        verbose_name=_('المستخدمون المصرح لهم')
    )
    show_in_menu = models.BooleanField(_('إظهار في القائمة'), default=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    def save(self, *args, **kwargs):
        """حفظ القسم ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للقسم
        if self.pk:
            self.clear_cache()

        # حفظ القسم
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """حذف القسم ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للقسم
        self.clear_cache()

        # حذف القسم
        super().delete(*args, **kwargs)

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت للقسم"""
        # مسح ذاكرة التخزين المؤقت لصلاحيات الوصول
        from django.contrib.auth import get_user_model
        User = get_user_model()
        for user in User.objects.all():
            cache_key = f'department_access_{self.id}_{user.id}'
            cache.delete(cache_key)

    class Meta:
        verbose_name = _('قسم')
        verbose_name_plural = _('الأقسام')
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def can_user_access(self, user):
        """التحقق مما إذا كان المستخدم يمكنه الوصول إلى هذا القسم"""
        # استخدام التخزين المؤقت لتحسين الأداء
        cache_key = f'department_access_{self.id}_{user.id}'
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            return cached_result

        # إذا لم يكن هناك نتيجة مخزنة مؤقتًا، نقوم بالحساب
        if not self.is_active:
            cache.set(cache_key, False, 3600)  # تخزين لمدة ساعة
            return False

        if user.user_type == 'admin':
            cache.set(cache_key, True, 3600)
            return True

        if self.access_level == 'all':
            cache.set(cache_key, True, 3600)
            return True

        if self.access_level == 'admin':
            result = user.user_type == 'admin'
            cache.set(cache_key, result, 3600)
            return result

        if self.access_level == 'specific':
            result = self.authorized_users.filter(id=user.id).exists()
            cache.set(cache_key, result, 3600)
            return result

        cache.set(cache_key, False, 3600)
        return False

class FieldGroup(models.Model):
    """مجموعة الحقول لتنظيم الحقول في مجموعات"""
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='field_groups', verbose_name=_('القسم'))
    name = models.CharField(_('اسم المجموعة'), max_length=100)
    description = models.TextField(_('وصف المجموعة'), blank=True, null=True)
    order = models.PositiveIntegerField(_('الترتيب'), default=0)
    is_collapsed = models.BooleanField(_('مطوية افتراضيًا'), default=False)

    class Meta:
        verbose_name = _('مجموعة حقول')
        verbose_name_plural = _('مجموعات الحقول')
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.name} ({self.department.name})"

class Field(models.Model):
    FIELD_TYPE_CHOICES = (
        # أنواع النصوص
        ('text', _('نص قصير')),
        ('textarea', _('نص طويل')),
        ('rich_text', _('نص منسق')),
        ('email', _('بريد إلكتروني')),
        ('url', _('رابط')),
        ('phone', _('رقم هاتف')),

        # أنواع الأرقام
        ('number', _('رقم')),
        ('decimal', _('رقم عشري')),
        ('currency', _('عملة')),
        ('percentage', _('نسبة مئوية')),

        # أنواع التواريخ
        ('date', _('تاريخ')),
        ('time', _('وقت')),
        ('datetime', _('تاريخ ووقت')),

        # أنواع الاختيارات
        ('select', _('قائمة منسدلة')),
        ('multi_select', _('اختيار متعدد')),
        ('radio', _('اختيار واحد')),
        ('checkbox', _('صندوق اختيار')),

        # أنواع الملفات
        ('file', _('ملف')),
        ('image', _('صورة')),
        ('pdf', _('ملف PDF')),

        # أنواع متقدمة
        ('signature', _('توقيع إلكتروني')),
        ('location', _('موقع جغرافي')),
        ('user', _('مستخدم')),
        ('department', _('قسم')),
        ('color', _('لون')),
    )

    # إضافة FIELD_TYPES كبديل لـ FIELD_TYPE_CHOICES
    FIELD_TYPES = FIELD_TYPE_CHOICES

    VALIDATION_TYPES = (
        ('none', _('بدون تحقق')),
        ('regex', _('تعبير منتظم')),
        ('min_length', _('الحد الأدنى للطول')),
        ('max_length', _('الحد الأقصى للطول')),
        ('min_value', _('الحد الأدنى للقيمة')),
        ('max_value', _('الحد الأقصى للقيمة')),
        ('email', _('بريد إلكتروني')),
        ('url', _('رابط')),
        ('phone', _('رقم هاتف')),
        ('custom', _('تحقق مخصص')),
    )

    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='fields', verbose_name=_('القسم'))
    group = models.ForeignKey(FieldGroup, on_delete=models.SET_NULL, null=True, blank=True, related_name='fields', verbose_name=_('المجموعة'))
    name = models.CharField(_('اسم الحقل'), max_length=100)
    display_name = models.CharField(_('اسم العرض'), max_length=100)
    description = models.TextField(_('وصف الحقل'), blank=True, null=True, help_text=_('وصف توضيحي للحقل يظهر للمستخدم'))
    type = models.CharField(_('نوع الحقل'), max_length=20, choices=FIELD_TYPE_CHOICES)
    required = models.BooleanField(_('مطلوب'), default=False)
    default_value = models.TextField(_('القيمة الافتراضية'), blank=True, null=True)
    placeholder = models.CharField(_('نص توضيحي'), max_length=200, blank=True, null=True)
    help_text = models.CharField(_('نص المساعدة'), max_length=200, blank=True, null=True)
    options = models.JSONField(_('خيارات'), blank=True, null=True)
    validation_type = models.CharField(_('نوع التحقق'), max_length=20, choices=VALIDATION_TYPES, default='none')
    validation_params = models.JSONField(_('معلمات التحقق'), blank=True, null=True)
    is_unique = models.BooleanField(_('قيمة فريدة'), default=False)
    is_searchable = models.BooleanField(_('قابل للبحث'), default=True)
    is_filterable = models.BooleanField(_('قابل للتصفية'), default=True)
    show_in_table = models.BooleanField(_('عرض في الجدول'), default=True)
    show_in_detail = models.BooleanField(_('عرض في التفاصيل'), default=True)
    show_in_export = models.BooleanField(_('عرض في التصدير'), default=True)
    is_readonly = models.BooleanField(_('للقراءة فقط'), default=False)
    is_hidden = models.BooleanField(_('مخفي'), default=False)
    order = models.PositiveIntegerField(_('الترتيب'), default=0)
    css_class = models.CharField(_('فئة CSS'), max_length=100, blank=True, null=True)
    width = models.CharField(_('العرض'), max_length=50, blank=True, null=True, help_text=_('مثال: 100%, 200px'))

    def save(self, *args, **kwargs):
        """حفظ الحقل ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للحقل
        if self.pk:
            self.clear_cache()

        # حفظ الحقل
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """حذف الحقل ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للحقل
        self.clear_cache()

        # حذف الحقل
        super().delete(*args, **kwargs)

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت للحقل"""
        # مسح ذاكرة التخزين المؤقت لخيارات الحقل
        cache_key = f'field_options_{self.id}'
        cache.delete(cache_key)

    class Meta:
        verbose_name = _('حقل')
        verbose_name_plural = _('الحقول')
        ordering = ['order', 'name']
        unique_together = ('department', 'name')

    def __str__(self):
        return f"{self.display_name} ({self.department.name})"

    def get_options_dict(self):
        """الحصول على خيارات الحقل كقاموس"""
        # استخدام التخزين المؤقت لتحسين الأداء
        cache_key = f'field_options_{self.id}'
        cached_options = cache.get(cache_key)

        if cached_options is not None:
            return cached_options

        # إذا لم يكن هناك خيارات مخزنة مؤقتًا، نقوم بالحساب
        if not self.options:
            cache.set(cache_key, {}, 3600)  # تخزين لمدة ساعة
            return {}

        try:
            # إذا كان الخيارات بالفعل قاموس (JSONField)
            if isinstance(self.options, dict):
                cache.set(cache_key, self.options, 3600)
                return self.options

            # إذا كان الخيارات نص JSON
            if isinstance(self.options, str):
                options_dict = json.loads(self.options)
                cache.set(cache_key, options_dict, 3600)
                return options_dict

            cache.set(cache_key, self.options, 3600)
            return self.options
        except (json.JSONDecodeError, TypeError, Exception):
            # إذا كانت الخيارات ليست بتنسيق JSON صالح، نحاول تحليلها كنص
            options_dict = {}
            try:
                # تقسيم النص إلى أسطر
                if isinstance(self.options, str):
                    lines = self.options.strip().split('\n')
                    for line in lines:
                        if ':' in line:
                            key, value = line.split(':', 1)
                            options_dict[key.strip()] = value.strip()
                        else:
                            # إذا لم يكن هناك فاصل، نستخدم القيمة كمفتاح وقيمة
                            value = line.strip()
                            if value:
                                options_dict[value] = value
            except Exception:
                pass

            cache.set(cache_key, options_dict, 3600)
            return options_dict

class DepartmentData(models.Model):
    """نموذج لتخزين بيانات الأقسام المخصصة"""
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='data_entries', verbose_name=_('القسم'))
    title = models.CharField(_('العنوان'), max_length=200)
    reference_number = models.CharField(_('الرقم المرجعي'), max_length=100, blank=True, null=True)
    data = models.JSONField(_('البيانات'))
    files = models.JSONField(_('الملفات'), blank=True, null=True)
    status = models.CharField(_('الحالة'), max_length=50, default='active')
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_department_data',
        verbose_name=_('تم الإنشاء بواسطة')
    )
    updated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_department_data',
        verbose_name=_('تم التحديث بواسطة')
    )
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    def save(self, *args, **kwargs):
        """حفظ البيانات ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للبيانات
        if self.pk:
            self.clear_cache()

        # حفظ البيانات
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """حذف البيانات ومسح ذاكرة التخزين المؤقت"""
        # مسح ذاكرة التخزين المؤقت للبيانات
        self.clear_cache()

        # حذف البيانات
        super().delete(*args, **kwargs)

    def clear_cache(self):
        """مسح ذاكرة التخزين المؤقت للبيانات"""
        # مسح ذاكرة التخزين المؤقت لجميع قيم البيانات
        if self.data:
            for field_name in self.data.keys():
                cache_key = f'department_data_value_{self.id}_{field_name}'
                cache.delete(cache_key)

    class Meta:
        verbose_name = _('بيانات القسم')
        verbose_name_plural = _('بيانات الأقسام')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.department.name})"

    def get_data_value(self, field_name):
        """الحصول على قيمة حقل معين من البيانات"""
        # استخدام التخزين المؤقت لتحسين الأداء
        cache_key = f'department_data_value_{self.id}_{field_name}'
        cached_value = cache.get(cache_key)

        if cached_value is not None:
            return cached_value

        # إذا لم يكن هناك قيمة مخزنة مؤقتًا، نقوم بالحساب
        if not self.data:
            cache.set(cache_key, None, 3600)  # تخزين لمدة ساعة
            return None

        value = self.data.get(field_name)

        # محاولة تحويل سلاسل التاريخ إلى كائنات تاريخ
        if value and isinstance(value, str) and field_name.startswith('date_'):
            try:
                import datetime
                value = datetime.date.fromisoformat(value)
            except (ValueError, TypeError):
                pass

        # تخزين القيمة في ذاكرة التخزين المؤقت
        cache.set(cache_key, value, 3600)
        return value

class DepartmentDataHistory(models.Model):
    """سجل تاريخ التغييرات على بيانات الأقسام"""
    data_entry = models.ForeignKey(DepartmentData, on_delete=models.CASCADE, related_name='history', verbose_name=_('بيانات القسم'))
    data_snapshot = models.JSONField(_('نسخة البيانات'))
    action = models.CharField(_('الإجراء'), max_length=50)  # create, update, delete
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('المستخدم')
    )
    timestamp = models.DateTimeField(_('التوقيت'), auto_now_add=True)

    class Meta:
        verbose_name = _('سجل تاريخ البيانات')
        verbose_name_plural = _('سجلات تاريخ البيانات')
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.action} - {self.data_entry.title} ({self.timestamp})"
