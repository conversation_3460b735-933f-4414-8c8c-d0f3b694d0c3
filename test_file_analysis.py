#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل ملف test123.xlsx للتأكد من قابليته للاستيراد
"""

import openpyxl

def analyze_test_file():
    """تحليل الملف وفحص البيانات الفارغة"""
    try:
        wb = openpyxl.load_workbook('test123.xlsx')
        ws = wb.active
        
        print('📊 تحليل ملف test123.xlsx')
        print('=' * 50)
        print(f'عدد الصفوف: {ws.max_row}')
        print(f'عدد الأعمدة: {ws.max_column}')
        print()
        
        # تحليل أول 5 صفوف من البيانات
        print('🔍 تحليل أول 5 صفوف:')
        print('-' * 30)
        
        for row_num in range(2, min(7, ws.max_row + 1)):
            row = list(ws.iter_rows(min_row=row_num, max_row=row_num, values_only=True))[0]
            
            # البيانات الأساسية
            order_number = row[0] if len(row) > 0 and row[0] is not None else ''
            committee_type = row[2] if len(row) > 2 and row[2] is not None else ''
            
            # عد الحقول الفارغة
            empty_count = 0
            filled_count = 0
            
            for value in row:
                if value is None or str(value).strip() == '':
                    empty_count += 1
                else:
                    filled_count += 1
            
            print(f'الصف {row_num}:')
            print(f'  رقم الأمر: {order_number or "فارغ"}')
            print(f'  النوع: {committee_type or "فارغ"}')
            print(f'  الحقول المملوءة: {filled_count}')
            print(f'  الحقول الفارغة: {empty_count}')
            print()
        
        # إحصائيات عامة
        total_cells = 0
        empty_cells = 0
        
        for row_num in range(2, ws.max_row + 1):
            row = list(ws.iter_rows(min_row=row_num, max_row=row_num, values_only=True))[0]
            for value in row:
                total_cells += 1
                if value is None or str(value).strip() == '':
                    empty_cells += 1
        
        filled_cells = total_cells - empty_cells
        fill_percentage = (filled_cells / total_cells) * 100 if total_cells > 0 else 0
        
        print('📈 إحصائيات عامة:')
        print('-' * 20)
        print(f'إجمالي الخلايا: {total_cells}')
        print(f'الخلايا المملوءة: {filled_cells}')
        print(f'الخلايا الفارغة: {empty_cells}')
        print(f'نسبة الملء: {fill_percentage:.1f}%')
        print()
        
        # النتيجة النهائية
        print('✅ النتيجة النهائية:')
        print('=' * 30)
        if fill_percentage > 20:  # إذا كان أكثر من 20% مملوء
            print('🎉 الملف قابل للاستيراد بنجاح!')
            print('💡 الحقول الفارغة ستُملأ بقيم افتراضية')
            print('🚀 يمكنك المتابعة بثقة')
        else:
            print('⚠️ الملف يحتوي على بيانات قليلة')
            print('💡 لكن لا يزال قابل للاستيراد')
        
    except Exception as e:
        print(f'❌ خطأ في قراءة الملف: {e}')

if __name__ == '__main__':
    analyze_test_file()
