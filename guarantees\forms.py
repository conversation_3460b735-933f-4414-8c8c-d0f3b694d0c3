from django import forms
from .models import Guarantee
from core.models import File

class GuaranteeForm(forms.ModelForm):
    class Meta:
        model = Guarantee
        fields = ['beneficiary_name', 'amount', 'duration', 'bank_name', 'start_date', 'end_date', 'notes']
        widgets = {
            'beneficiary_name': forms.TextInput(attrs={'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control'}),
            'duration': forms.NumberInput(attrs={'class': 'form-control', 'onchange': 'updateEndDate()'}),
            'bank_name': forms.TextInput(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

class GuaranteeExtendForm(forms.Form):
    days = forms.IntegerField(
        label='عدد أيام التمديد',
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

class FileUploadForm(forms.ModelForm):
    class Meta:
        model = File
        fields = ['name', 'file']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'file': forms.FileInput(attrs={'class': 'form-control'}),
        }
