from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta

class Guarantee(models.Model):
    beneficiary_name = models.CharField(_('اسم المستفيد'), max_length=100)
    amount = models.DecimalField(_('مبلغ الكفالة'), max_digits=15, decimal_places=2)
    duration = models.PositiveIntegerField(_('مدة الكفالة (بالأيام)'))
    bank_name = models.CharField(_('اسم المصرف'), max_length=100)
    start_date = models.DateField(_('تاريخ بدء الكفالة'))
    end_date = models.DateField(_('تاريخ انتهاء الكفالة'))
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='guarantees', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('كفالة')
        verbose_name_plural = _('الكفالات')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.beneficiary_name} - {self.amount}"

    def is_expired(self):
        return self.end_date < timezone.now().date()

    def is_expiring_soon(self):
        return not self.is_expired() and self.end_date <= (timezone.now().date() + timedelta(days=7))

    def extend(self, days):
        self.end_date = self.end_date + timedelta(days=days)
        self.duration += days
        self.save()
