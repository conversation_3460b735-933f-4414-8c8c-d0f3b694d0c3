from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class DepartmentsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'departments'
    verbose_name = _('الأقسام')

    def ready(self):
        # تسجيل مكتبة القوالب عند تحميل التطبيق
        from django.template import library
        import os

        # التأكد من وجود مجلد templatetags
        templatetags_dir = os.path.join(os.path.dirname(__file__), 'templatetags')
        if not os.path.exists(templatetags_dir):
            os.makedirs(templatetags_dir)

        # التأكد من وجود ملف __init__.py
        init_file = os.path.join(templatetags_dir, '__init__.py')
        if not os.path.exists(init_file):
            with open(init_file, 'w') as f:
                f.write('# This file is required for Python to recognize this directory as a package')
