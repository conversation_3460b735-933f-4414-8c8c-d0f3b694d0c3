[{"model": "accounts.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$1000000$ZtGFiYJje0nOwHy4wHO5AH$UHCLYQkNyHOneWk+6u6hDYPzSzvMohItz66dyHdI+vE=", "last_login": "2025-04-24T10:38:16.592Z", "is_superuser": true, "username": "dsr", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-04-19T00:14:27.216Z", "user_type": "data_entry", "department": null, "groups": [], "user_permissions": []}}, {"model": "accounts.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$1000000$FXRrU1Em2MkA31Weez9vBf$Sbklfoqnu9EyLGi/mEvL4f9FzOm0cz3QOewKXT6FHuk=", "last_login": "2025-04-27T05:18:39.017Z", "is_superuser": true, "username": "admin", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-04-19T01:01:12.208Z", "user_type": "admin", "department": null, "groups": [], "user_permissions": []}}, {"model": "departments.department", "pk": 4, "fields": {"name": "قسم الخوادم والانظمة", "description": "اتمتة الاجراءات", "icon": "fa-folder", "color": "#3498db", "table_name": "___data", "order": 1, "manager": 2, "access_level": "specific", "show_in_menu": true, "is_active": true, "created_at": "2025-04-24T11:58:24.450Z", "updated_at": "2025-04-27T05:25:22.143Z", "authorized_users": []}}, {"model": "departments.field", "pk": 7, "fields": {"department": 4, "group": null, "name": "text_field_849", "display_name": "حقل نص", "description": "", "type": "text", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 0, "css_class": null, "width": null}}, {"model": "departments.field", "pk": 8, "fields": {"department": 4, "group": null, "name": "text_field_811", "display_name": "<PERSON>قل ", "description": "", "type": "text", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 1, "css_class": null, "width": null}}, {"model": "departments.field", "pk": 9, "fields": {"department": 4, "group": null, "name": "number_field_7", "display_name": "حقل رقم", "description": "", "type": "number", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 2, "css_class": null, "width": null}}, {"model": "departments.field", "pk": 10, "fields": {"department": 4, "group": null, "name": "date_field_160", "display_name": "حقل تاريخ", "description": "", "type": "date", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 3, "css_class": null, "width": null}}, {"model": "departments.field", "pk": 11, "fields": {"department": 4, "group": null, "name": "select_field_996", "display_name": "قائمة منسدلة", "description": "", "type": "select", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {"option1": "الخيار الأول", "option2": "الخيار الثاني", "option3": "الخيار الثالث"}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 0, "css_class": null, "width": null}}, {"model": "departments.field", "pk": 12, "fields": {"department": 4, "group": null, "name": "checkbox_field_473", "display_name": "مربع اختيار", "description": "", "type": "checkbox", "required": false, "default_value": "", "placeholder": "", "help_text": "", "options": {}, "validation_type": "none", "validation_params": null, "is_unique": false, "is_searchable": true, "is_filterable": true, "show_in_table": true, "show_in_detail": true, "show_in_export": true, "is_readonly": false, "is_hidden": false, "order": 1, "css_class": null, "width": null}}, {"model": "departments.departmentdata", "pk": 1, "fields": {"department": 4, "title": "ما ادري", "reference_number": "4", "data": {"text_field_849": "دريد", "text_field_811": "ع<PERSON><PERSON><PERSON>", "number_field_7": 1, "date_field_160": "2025-04-24"}, "files": null, "status": "active", "created_by": 2, "updated_by": 2, "created_at": "2025-04-24T12:36:49.947Z", "updated_at": "2025-04-24T12:36:49.947Z"}}, {"model": "departments.departmentdata", "pk": 2, "fields": {"department": 4, "title": "بيانات جديدة", "reference_number": null, "data": {"text_field_849": "<PERSON><PERSON><PERSON><PERSON>", "text_field_811": "قاسم", "number_field_7": 88, "date_field_160": "2025-04-01"}, "files": null, "status": "active", "created_by": 2, "updated_by": 2, "created_at": "2025-04-24T12:49:36.774Z", "updated_at": "2025-04-24T12:49:36.774Z"}}, {"model": "departments.departmentdata", "pk": 3, "fields": {"department": 4, "title": "بيانات جديدة", "reference_number": null, "data": {"text_field_849": "", "select_field_996": "", "text_field_811": "", "checkbox_field_473": false, "number_field_7": null, "date_field_160": null}, "files": null, "status": "active", "created_by": 2, "updated_by": 2, "created_at": "2025-04-24T13:10:13.966Z", "updated_at": "2025-04-24T13:10:13.966Z"}}, {"model": "departments.departmentdatahistory", "pk": 1, "fields": {"data_entry": 1, "data_snapshot": {"text_field_849": "دريد", "text_field_811": "ع<PERSON><PERSON><PERSON>", "number_field_7": 1, "date_field_160": "2025-04-24"}, "action": "create", "user": 2, "timestamp": "2025-04-24T12:36:49.961Z"}}, {"model": "departments.departmentdatahistory", "pk": 2, "fields": {"data_entry": 2, "data_snapshot": {"text_field_849": "<PERSON><PERSON><PERSON><PERSON>", "text_field_811": "قاسم", "number_field_7": 88, "date_field_160": "2025-04-01"}, "action": "create", "user": 2, "timestamp": "2025-04-24T12:49:36.789Z"}}, {"model": "departments.departmentdatahistory", "pk": 3, "fields": {"data_entry": 3, "data_snapshot": {"text_field_849": "", "select_field_996": "", "text_field_811": "", "checkbox_field_473": false, "number_field_7": null, "date_field_160": null}, "action": "create", "user": 2, "timestamp": "2025-04-24T13:10:14.029Z"}}, {"model": "committees.committee", "pk": 1, "fields": {"title": "امر اداري", "order_number": "234", "order_date": "2025-04-10", "type": "investigation", "responsible_entity": null, "duration": 25, "end_date": "2025-05-05", "status": "active", "last_action": "", "notes": "", "created_by": 2, "created_at": "2025-04-19T01:28:16.622Z", "updated_at": "2025-04-19T01:28:16.622Z"}}, {"model": "committees.committee", "pk": 2, "fields": {"title": "فس<PERSON> عقد", "order_number": "78655", "order_date": "2025-04-01", "type": "joint", "responsible_entity": null, "duration": 60, "end_date": "2025-05-31", "status": "active", "last_action": "نقل منتسب\r\nمفاتحة دوائر", "notes": "", "created_by": 2, "created_at": "2025-04-24T10:14:54.559Z", "updated_at": "2025-04-24T10:17:09.797Z"}}, {"model": "committees.committee", "pk": 3, "fields": {"title": "تضرر عجلة", "order_number": "9980", "order_date": "2025-04-01", "type": "investigation", "responsible_entity": null, "duration": 15, "end_date": "2025-04-16", "status": "closed", "last_action": "", "notes": "", "created_by": 2, "created_at": "2025-04-24T10:18:29.234Z", "updated_at": "2025-04-24T10:31:53.311Z"}}, {"model": "committees.committee", "pk": 4, "fields": {"title": "فقدان سلاح", "order_number": "53340", "order_date": "2025-04-01", "type": "council", "responsible_entity": null, "duration": 15, "end_date": "2025-04-16", "status": "closed", "last_action": "", "notes": "", "created_by": 2, "created_at": "2025-04-24T10:20:36.252Z", "updated_at": "2025-04-24T10:22:05.305Z"}}, {"model": "committees.committee", "pk": 5, "fields": {"title": "انقلاب عجلة", "order_number": "29634", "order_date": "2025-04-27", "type": "investigation", "responsible_entity": null, "duration": 15, "end_date": "2025-05-12", "status": "active", "last_action": "تشكيل جديد\r\nاضافة اعضاء اللجنة\r\nمخاطبة دائرة الكمارك", "notes": "لاتوجد", "created_by": 2, "created_at": "2025-04-27T05:19:38.004Z", "updated_at": "2025-04-27T05:21:57.817Z"}}, {"model": "committees.committeemember", "pk": 1, "fields": {"committee": 1, "name": "د<PERSON><PERSON><PERSON> عدنان", "role": "chairman"}}, {"model": "committees.committeemember", "pk": 2, "fields": {"committee": 1, "name": "علي جواد", "role": "member"}}, {"model": "committees.committeemember", "pk": 3, "fields": {"committee": 1, "name": "<PERSON>", "role": "member"}}, {"model": "committees.committeemember", "pk": 4, "fields": {"committee": 2, "name": "د<PERSON><PERSON><PERSON> عدنان", "role": "chairman"}}, {"model": "committees.committeemember", "pk": 5, "fields": {"committee": 2, "name": "علي جواد", "role": "member"}}, {"model": "committees.committeemember", "pk": 6, "fields": {"committee": 2, "name": "<PERSON>", "role": "member"}}, {"model": "committees.committeemember", "pk": 7, "fields": {"committee": 3, "name": "<PERSON>", "role": "chairman"}}, {"model": "committees.committeemember", "pk": 8, "fields": {"committee": 3, "name": "علي جواد", "role": "member"}}, {"model": "committees.committeemember", "pk": 9, "fields": {"committee": 3, "name": "د<PERSON><PERSON><PERSON> عدنان", "role": "secretary"}}, {"model": "committees.committeemember", "pk": 10, "fields": {"committee": 4, "name": "د<PERSON><PERSON><PERSON> عدنان", "role": "secretary"}}, {"model": "committees.committeemember", "pk": 11, "fields": {"committee": 4, "name": "علي جواد", "role": "chairman"}}, {"model": "committees.committeemember", "pk": 12, "fields": {"committee": 4, "name": "<PERSON>", "role": "member"}}, {"model": "committees.committeemember", "pk": 13, "fields": {"committee": 5, "name": "<PERSON><PERSON><PERSON><PERSON> جواد", "role": "chairman"}}, {"model": "committees.committeemember", "pk": 14, "fields": {"committee": 5, "name": "علي كامل", "role": "member"}}, {"model": "committees.committeemember", "pk": 15, "fields": {"committee": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "role": "member"}}, {"model": "guarantees.guarantee", "pk": 1, "fields": {"beneficiary_name": "duraid", "amount": "5000000.00", "duration": 360, "bank_name": "مصرف التنمية", "start_date": "2025-04-24", "end_date": "2026-04-19", "notes": "nothing", "created_by": 2, "created_at": "2025-04-24T10:00:51.568Z", "updated_at": "2025-04-24T10:00:51.568Z"}}, {"model": "core.file", "pk": 1, "fields": {"name": "علي جواد", "file": "files/2025/04/19/112233.pdf", "content_type": 9, "object_id": 1, "uploaded_by": 2, "uploaded_at": "2025-04-19T01:28:33.363Z"}}, {"model": "core.file", "pk": 2, "fields": {"name": "bank statment", "file": "files/2025/04/24/Screenshot_2025-04-21_061231.png", "content_type": 11, "object_id": 1, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:02:31.598Z"}}, {"model": "core.file", "pk": 3, "fields": {"name": "امر التشكيل", "file": "files/2025/04/24/Screenshot_2025-04-21_061231_0vO01gu.png", "content_type": 9, "object_id": 2, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:15:12.670Z"}}, {"model": "core.file", "pk": 4, "fields": {"name": "كتاب الكمارك", "file": "files/2025/04/24/Screenshot_2025-04-21_061231_1tRRiJm.png", "content_type": 9, "object_id": 2, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:15:39.684Z"}}, {"model": "core.file", "pk": 5, "fields": {"name": "قسم الدعاوى والمجالس التحقيقية", "file": "files/2025/04/24/Screenshot_2025-04-23_192304.png", "content_type": 9, "object_id": 2, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:17:30.283Z"}}, {"model": "core.file", "pk": 6, "fields": {"name": "نوع المجلس", "file": "files/2025/04/24/Screenshot_2025-04-23_203425.png", "content_type": 9, "object_id": 3, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:18:43.179Z"}}, {"model": "core.file", "pk": 7, "fields": {"name": "قسم الدعاوى والمجالس التحقيقية", "file": "files/2025/04/24/Screenshot_2025-04-13_235431.png", "content_type": 9, "object_id": 4, "uploaded_by": 2, "uploaded_at": "2025-04-24T10:21:21.058Z"}}, {"model": "core.file", "pk": 8, "fields": {"name": "امر التشكيل", "file": "files/2025/04/27/Screenshot_2025-04-24_155941.png", "content_type": 9, "object_id": 5, "uploaded_by": 2, "uploaded_at": "2025-04-27T05:19:55.321Z"}}, {"model": "core.file", "pk": 9, "fields": {"name": "اسماء الاعضاء والامر", "file": "files/2025/04/27/Screenshot_2025-04-24_145040.png", "content_type": 9, "object_id": 5, "uploaded_by": 2, "uploaded_at": "2025-04-27T05:20:16.929Z"}}, {"model": "core.dynamicfield", "pk": 1, "fields": {"section": "committees", "name": "title", "display_name": "عنوان اللجنة", "field_type": "text", "required": true, "options": null, "order": 1, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.088Z", "updated_at": "2025-04-19T09:57:30.088Z"}}, {"model": "core.dynamicfield", "pk": 2, "fields": {"section": "committees", "name": "order_number", "display_name": "رقم الأمر الإداري", "field_type": "text", "required": true, "options": null, "order": 2, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.104Z", "updated_at": "2025-04-19T09:57:30.104Z"}}, {"model": "core.dynamicfield", "pk": 3, "fields": {"section": "committees", "name": "order_date", "display_name": "تاريخ التشكيل", "field_type": "date", "required": true, "options": null, "order": 3, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.118Z", "updated_at": "2025-04-19T09:57:30.118Z"}}, {"model": "core.dynamicfield", "pk": 4, "fields": {"section": "committees", "name": "type", "display_name": "نوع اللجنة", "field_type": "select", "required": true, "options": "{\"investigation\": \"لجنة تحقيقية\", \"council\": \"مجلس تحقيقي\", \"joint\": \"لجنة مشتركة\"}", "order": 4, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.132Z", "updated_at": "2025-04-19T09:57:30.132Z"}}, {"model": "core.dynamicfield", "pk": 5, "fields": {"section": "committees", "name": "duration", "display_name": "مدة اللجنة (بالأيام)", "field_type": "number", "required": true, "options": null, "order": 5, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.146Z", "updated_at": "2025-04-19T09:57:30.146Z"}}, {"model": "core.dynamicfield", "pk": 6, "fields": {"section": "committees", "name": "end_date", "display_name": "تاريخ انتهاء اللجنة", "field_type": "date", "required": true, "options": null, "order": 6, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.160Z", "updated_at": "2025-04-19T09:57:30.160Z"}}, {"model": "core.dynamicfield", "pk": 7, "fields": {"section": "committees", "name": "status", "display_name": "حالة اللجنة", "field_type": "select", "required": true, "options": "{\"active\": \"نشطة\", \"extended\": \"ممددة\", \"closed\": \"مغلقة\"}", "order": 7, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.173Z", "updated_at": "2025-04-19T09:57:30.173Z"}}, {"model": "core.dynamicfield", "pk": 8, "fields": {"section": "committees", "name": "last_action", "display_name": "آخر الإجراءات", "field_type": "textarea", "required": false, "options": null, "order": 8, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.187Z", "updated_at": "2025-04-19T09:57:30.187Z"}}, {"model": "core.dynamicfield", "pk": 9, "fields": {"section": "committees", "name": "notes", "display_name": "ملاحظات", "field_type": "textarea", "required": false, "options": null, "order": 9, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.201Z", "updated_at": "2025-04-19T09:57:30.201Z"}}, {"model": "core.dynamicfield", "pk": 10, "fields": {"section": "committees", "name": "responsible_entity", "display_name": "الجهة المسؤولة", "field_type": "text", "required": false, "options": null, "order": 10, "is_active": true, "is_default": false, "created_by": 2, "created_at": "2025-04-19T09:57:30.214Z", "updated_at": "2025-04-19T09:57:30.214Z"}}, {"model": "core.dynamicfield", "pk": 11, "fields": {"section": "guarantees", "name": "title", "display_name": "عنوان الكفالة", "field_type": "text", "required": true, "options": null, "order": 1, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.237Z", "updated_at": "2025-04-19T09:57:30.237Z"}}, {"model": "core.dynamicfield", "pk": 12, "fields": {"section": "guarantees", "name": "reference_number", "display_name": "رقم المرجع", "field_type": "text", "required": true, "options": null, "order": 2, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.251Z", "updated_at": "2025-04-19T09:57:30.251Z"}}, {"model": "core.dynamicfield", "pk": 13, "fields": {"section": "guarantees", "name": "type", "display_name": "نوع الكفالة", "field_type": "select", "required": true, "options": "{\"bank\": \"كفالة بنكية\", \"personal\": \"كفالة شخصية\", \"corporate\": \"كفالة شركة\"}", "order": 3, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.264Z", "updated_at": "2025-04-19T09:57:30.264Z"}}, {"model": "core.dynamicfield", "pk": 14, "fields": {"section": "guarantees", "name": "amount", "display_name": "المبلغ", "field_type": "number", "required": true, "options": null, "order": 4, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.279Z", "updated_at": "2025-04-19T09:57:30.279Z"}}, {"model": "core.dynamicfield", "pk": 15, "fields": {"section": "guarantees", "name": "start_date", "display_name": "تاريخ البدء", "field_type": "date", "required": true, "options": null, "order": 5, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.292Z", "updated_at": "2025-04-19T09:57:30.292Z"}}, {"model": "core.dynamicfield", "pk": 16, "fields": {"section": "guarantees", "name": "end_date", "display_name": "تاريخ الانتهاء", "field_type": "date", "required": true, "options": null, "order": 6, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.306Z", "updated_at": "2025-04-19T09:57:30.306Z"}}, {"model": "core.dynamicfield", "pk": 17, "fields": {"section": "guarantees", "name": "status", "display_name": "الحالة", "field_type": "select", "required": true, "options": "{\"active\": \"نشطة\", \"expired\": \"منتهية\", \"cancelled\": \"ملغاة\"}", "order": 7, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.319Z", "updated_at": "2025-04-19T09:57:30.320Z"}}, {"model": "core.dynamicfield", "pk": 18, "fields": {"section": "guarantees", "name": "notes", "display_name": "ملاحظات", "field_type": "textarea", "required": false, "options": null, "order": 8, "is_active": true, "is_default": true, "created_by": 2, "created_at": "2025-04-19T09:57:30.334Z", "updated_at": "2025-04-19T09:57:30.334Z"}}, {"model": "core.dynamicfield", "pk": 19, "fields": {"section": "guarantees", "name": "issuer", "display_name": "الجهة المصدرة", "field_type": "text", "required": false, "options": null, "order": 9, "is_active": true, "is_default": false, "created_by": 2, "created_at": "2025-04-19T09:57:30.348Z", "updated_at": "2025-04-19T09:57:30.348Z"}}]