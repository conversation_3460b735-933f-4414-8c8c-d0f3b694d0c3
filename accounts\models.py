from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    USER_TYPE_CHOICES = (
        ('admin', _('مدير النظام')),
        ('department_manager', _('مدير قسم')),
        ('data_entry', _('مدخل بيانات')),
    )

    user_type = models.CharField(_('نوع المستخدم'), max_length=20, choices=USER_TYPE_CHOICES, default='data_entry')

    class Meta:
        verbose_name = _('مستخدم')
        verbose_name_plural = _('المستخدمين')

    def get_initials(self):
        """الحصول على الأحرف الأولى من اسم المستخدم"""
        if self.first_name and self.last_name:
            return f"{self.first_name[0]}{self.last_name[0]}".upper()
        elif self.first_name:
            return self.first_name[0].upper()
        elif self.last_name:
            return self.last_name[0].upper()
        else:
            return self.username[0].upper()
