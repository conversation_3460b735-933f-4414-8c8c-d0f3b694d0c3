{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}
{% if guarantee %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if guarantee %}تعديل كفالة{% else %}إضافة كفالة جديدة{% endif %}</h1>
    <a href="{% url 'guarantees:list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى قائمة الكفالات
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if guarantee %}تعديل كفالة{% else %}إضا<PERSON>ة كفالة جديدة{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.beneficiary_name.id_for_label }}">{{ form.beneficiary_name.label }}</label>
                        {{ form.beneficiary_name }}
                        {% if form.beneficiary_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.beneficiary_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.amount.id_for_label }}">{{ form.amount.label }}</label>
                        {{ form.amount }}
                        {% if form.amount.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.amount.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.bank_name.id_for_label }}">{{ form.bank_name.label }}</label>
                        {{ form.bank_name }}
                        {% if form.bank_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.bank_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.duration.id_for_label }}">{{ form.duration.label }}</label>
                        {{ form.duration }}
                        {% if form.duration.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.duration.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.start_date.id_for_label }}">{{ form.start_date.label }}</label>
                        {{ form.start_date }}
                        {% if form.start_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.start_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.end_date.id_for_label }}">{{ form.end_date.label }}</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'guarantees:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث تاريخ الانتهاء بناءً على تاريخ البدء والمدة
    function updateEndDate() {
        const startDateInput = document.getElementById('id_start_date');
        const durationInput = document.getElementById('id_duration');
        const endDateInput = document.getElementById('id_end_date');
        
        if (startDateInput && durationInput && endDateInput) {
            const startDate = new Date(startDateInput.value);
            const duration = parseInt(durationInput.value);
            
            if (!isNaN(startDate.getTime()) && !isNaN(duration)) {
                const endDate = new Date(startDate);
                endDate.setDate(endDate.getDate() + duration);
                
                const year = endDate.getFullYear();
                const month = String(endDate.getMonth() + 1).padStart(2, '0');
                const day = String(endDate.getDate()).padStart(2, '0');
                
                endDateInput.value = `${year}-${month}-${day}`;
            }
        }
    }
    
    // تحديث تاريخ الانتهاء عند تغيير تاريخ البدء أو المدة
    document.getElementById('id_start_date').addEventListener('change', updateEndDate);
    document.getElementById('id_duration').addEventListener('change', updateEndDate);
    
    // تحديث تاريخ الانتهاء عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', updateEndDate);
</script>
{% endblock %}
