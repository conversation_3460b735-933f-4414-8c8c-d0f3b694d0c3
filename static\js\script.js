// تفعيل التلميحات
$(function () {
    $('[data-toggle="tooltip"]').tooltip();
});

// تفعيل النوافذ المنبثقة
$(function () {
    $('[data-toggle="popover"]').popover();
});

// تأكيد الحذف
function confirmDelete(event, message) {
    if (!confirm(message || 'هل أنت متأكد من الحذف؟')) {
        event.preventDefault();
    }
}

// تحديث حقل التاريخ النهائي بناءً على تاريخ البدء والمدة
function updateEndDate() {
    const startDateInput = document.getElementById('id_start_date');
    const durationInput = document.getElementById('id_duration');
    const endDateInput = document.getElementById('id_end_date');
    
    if (startDateInput && durationInput && endDateInput) {
        const startDate = new Date(startDateInput.value);
        const duration = parseInt(durationInput.value);
        
        if (!isNaN(startDate.getTime()) && !isNaN(duration)) {
            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + duration);
            
            const year = endDate.getFullYear();
            const month = String(endDate.getMonth() + 1).padStart(2, '0');
            const day = String(endDate.getDate()).padStart(2, '0');
            
            endDateInput.value = `${year}-${month}-${day}`;
        }
    }
}

// تحديث الحقول الديناميكية بناءً على نوع الحقل
function updateFieldOptions() {
    const fieldTypeSelect = document.getElementById('id_type');
    const optionsDiv = document.getElementById('options-container');
    
    if (fieldTypeSelect && optionsDiv) {
        const fieldType = fieldTypeSelect.value;
        
        if (fieldType === 'select') {
            optionsDiv.style.display = 'block';
        } else {
            optionsDiv.style.display = 'none';
        }
    }
}
