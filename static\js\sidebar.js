// وظائف القائمة الجانبية

document.addEventListener('DOMContentLoaded', function() {
    // تبديل حالة القائمة الجانبية (مطوية/مفتوحة)
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-collapsed');
            // حفظ حالة القائمة في التخزين المحلي
            localStorage.setItem('sidebar-collapsed', document.body.classList.contains('sidebar-collapsed'));
        });
    }
    
    // استعادة حالة القائمة من التخزين المحلي
    if (localStorage.getItem('sidebar-collapsed') === 'true') {
        document.body.classList.add('sidebar-collapsed');
    }
    
    // فتح/إغلاق القائمة على الأجهزة المحمولة
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-mobile-open');
        });
    }
    
    // إغلاق القائمة عند النقر خارجها على الأجهزة المحمولة
    const sidebarOverlay = document.querySelector('.sidebar-overlay');
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            document.body.classList.remove('sidebar-mobile-open');
        });
    }
    
    // تبديل حالة القوائم المنسدلة
    const dropdownToggles = document.querySelectorAll('.sidebar-dropdown-toggle');
    dropdownToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إغلاق جميع القوائم المنسدلة الأخرى
            if (!e.currentTarget.classList.contains('active')) {
                dropdownToggles.forEach(function(otherToggle) {
                    if (otherToggle !== e.currentTarget) {
                        otherToggle.classList.remove('active');
                        otherToggle.setAttribute('aria-expanded', 'false');
                        const dropdown = otherToggle.nextElementSibling;
                        if (dropdown) {
                            dropdown.classList.remove('show');
                        }
                    }
                });
            }
            
            // تبديل حالة القائمة المنسدلة الحالية
            e.currentTarget.classList.toggle('active');
            const expanded = e.currentTarget.getAttribute('aria-expanded') === 'true' || false;
            e.currentTarget.setAttribute('aria-expanded', !expanded);
            
            const dropdown = e.currentTarget.nextElementSibling;
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        });
    });
    
    // تفعيل الرابط الحالي
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-link, .sidebar-dropdown-item');
    
    let activeLink = null;
    let maxMatchLength = 0;
    
    sidebarLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/' && href.length > maxMatchLength) {
            activeLink = link;
            maxMatchLength = href.length;
        }
    });
    
    if (activeLink) {
        activeLink.classList.add('active');
        
        // إذا كان الرابط النشط في قائمة منسدلة، نفتح القائمة المنسدلة
        if (activeLink.classList.contains('sidebar-dropdown-item')) {
            const parentDropdown = activeLink.closest('.sidebar-dropdown');
            if (parentDropdown) {
                parentDropdown.classList.add('show');
                const parentToggle = parentDropdown.previousElementSibling;
                if (parentToggle) {
                    parentToggle.classList.add('active');
                    parentToggle.setAttribute('aria-expanded', 'true');
                }
            }
        }
    }
    
    // تبديل قائمة الإشعارات
    const notificationsToggle = document.getElementById('notifications-toggle');
    if (notificationsToggle) {
        notificationsToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const menu = document.querySelector('.notifications-menu');
            if (menu) {
                menu.classList.toggle('show');
            }
        });
        
        // إغلاق قائمة الإشعارات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.notifications-dropdown')) {
                const menu = document.querySelector('.notifications-menu');
                if (menu && menu.classList.contains('show')) {
                    menu.classList.remove('show');
                }
            }
        });
    }
});
