from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse
from committees.models import Committee
from guarantees.models import Guarantee
from django.utils import timezone
from datetime import timedelta
import csv
import xlwt

@login_required
def index(request):
    return render(request, 'reports/index.html')

@login_required
def committees_report(request):
    # الحصول على معايير التصفية
    status = request.GET.get('status', '')
    type = request.GET.get('type', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # الحصول على جميع اللجان
    committees = Committee.objects.all()

    # تطبيق معايير التصفية
    if status:
        committees = committees.filter(status=status)

    if type:
        committees = committees.filter(type=type)

    if date_from:
        committees = committees.filter(order_date__gte=date_from)

    if date_to:
        committees = committees.filter(order_date__lte=date_to)

    # الإحصائيات
    total_committees = committees.count()
    active_committees = committees.filter(status='active').count()
    closed_committees = committees.filter(status='closed').count()
    extended_committees = committees.filter(status='extended').count()

    context = {
        'committees': committees,
        'total_committees': total_committees,
        'active_committees': active_committees,
        'closed_committees': closed_committees,
        'extended_committees': extended_committees,
        'status': status,
        'type': type,
        'date_from': date_from,
        'date_to': date_to,
    }

    return render(request, 'reports/committees_report.html', context)

@login_required
def guarantees_report(request):
    # الحصول على معايير التصفية
    bank = request.GET.get('bank', '')
    status = request.GET.get('status', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # الحصول على جميع الكفالات
    guarantees = Guarantee.objects.all()

    # تطبيق معايير التصفية
    if bank:
        guarantees = guarantees.filter(bank_name=bank)

    if status == 'active':
        guarantees = guarantees.filter(end_date__gte=timezone.now().date())
    elif status == 'expired':
        guarantees = guarantees.filter(end_date__lt=timezone.now().date())
    elif status == 'expiring_soon':
        guarantees = guarantees.filter(
            end_date__lte=timezone.now().date() + timedelta(days=30),
            end_date__gte=timezone.now().date()
        )

    if date_from:
        guarantees = guarantees.filter(start_date__gte=date_from)

    if date_to:
        guarantees = guarantees.filter(start_date__lte=date_to)

    # الإحصائيات
    total_guarantees = guarantees.count()
    active_guarantees = Guarantee.objects.filter(end_date__gte=timezone.now().date()).count()
    expired_guarantees = Guarantee.objects.filter(end_date__lt=timezone.now().date()).count()
    total_amount = sum(guarantee.amount for guarantee in guarantees)

    # الحصول على قائمة البنوك
    banks = Guarantee.objects.values_list('bank_name', flat=True).distinct()

    context = {
        'guarantees': guarantees,
        'total_guarantees': total_guarantees,
        'active_guarantees': active_guarantees,
        'expired_guarantees': expired_guarantees,
        'total_amount': total_amount,
        'banks': banks,
        'bank': bank,
        'status': status,
        'date_from': date_from,
        'date_to': date_to,
    }

    return render(request, 'reports/guarantees_report.html', context)

@login_required
def export_report(request, report_type):
    # الحصول على نوع التصدير
    export_type = request.GET.get('export_type', 'csv')

    if report_type == 'committees':
        # الحصول على معايير التصفية
        status = request.GET.get('status', '')
        type = request.GET.get('type', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')

        # الحصول على جميع اللجان
        committees = Committee.objects.all()

        # تطبيق معايير التصفية
        if status:
            committees = committees.filter(status=status)

        if type:
            committees = committees.filter(type=type)

        if date_from:
            committees = committees.filter(order_date__gte=date_from)

        if date_to:
            committees = committees.filter(order_date__lte=date_to)

        if export_type == 'csv':
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="committees_report.csv"'

            writer = csv.writer(response)
            writer.writerow(['#', 'العنوان', 'رقم الأمر', 'تاريخ التشكيل', 'النوع', 'الحالة', 'تاريخ الانتهاء'])

            for i, committee in enumerate(committees, 1):
                writer.writerow([i, committee.title, committee.order_number, committee.order_date, committee.get_type_display(), committee.get_status_display(), committee.end_date])

            return response

        elif export_type == 'excel':
            response = HttpResponse(content_type='application/ms-excel')
            response['Content-Disposition'] = 'attachment; filename="committees_report.xls"'

            wb = xlwt.Workbook(encoding='utf-8')
            ws = wb.add_sheet('تقرير اللجان')

            # أنماط الخلايا
            font_style = xlwt.XFStyle()
            font_style.font.bold = True

            # عناوين الأعمدة
            columns = ['#', 'العنوان', 'رقم الأمر', 'تاريخ التشكيل', 'النوع', 'الحالة', 'تاريخ الانتهاء']

            # كتابة العناوين
            for col_num, column_title in enumerate(columns):
                ws.write(0, col_num, column_title, font_style)

            # كتابة البيانات
            for i, committee in enumerate(committees, 1):
                row_num = i
                ws.write(row_num, 0, i)
                ws.write(row_num, 1, committee.title)
                ws.write(row_num, 2, committee.order_number)
                ws.write(row_num, 3, str(committee.order_date))
                ws.write(row_num, 4, committee.get_type_display())
                ws.write(row_num, 5, committee.get_status_display())
                ws.write(row_num, 6, str(committee.end_date))

            wb.save(response)
            return response

    elif report_type == 'guarantees':
        # الحصول على معايير التصفية
        bank = request.GET.get('bank', '')
        status = request.GET.get('status', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')

        # الحصول على جميع الكفالات
        guarantees = Guarantee.objects.all()

        # تطبيق معايير التصفية
        if bank:
            guarantees = guarantees.filter(bank_name=bank)

        if status == 'active':
            guarantees = guarantees.filter(end_date__gte=timezone.now().date())
        elif status == 'expired':
            guarantees = guarantees.filter(end_date__lt=timezone.now().date())
        elif status == 'expiring_soon':
            guarantees = guarantees.filter(
                end_date__lte=timezone.now().date() + timedelta(days=30),
                end_date__gte=timezone.now().date()
            )

        if date_from:
            guarantees = guarantees.filter(start_date__gte=date_from)

        if date_to:
            guarantees = guarantees.filter(start_date__lte=date_to)

        if export_type == 'csv':
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename="guarantees_report.csv"'

            writer = csv.writer(response)
            writer.writerow(['#', 'اسم المستفيد', 'المبلغ', 'المصرف', 'تاريخ البدء', 'تاريخ الانتهاء', 'الحالة'])

            for i, guarantee in enumerate(guarantees, 1):
                status = 'نشطة' if not guarantee.is_expired() else 'منتهية'
                writer.writerow([i, guarantee.beneficiary_name, guarantee.amount, guarantee.bank_name, guarantee.start_date, guarantee.end_date, status])

            return response

        elif export_type == 'excel':
            response = HttpResponse(content_type='application/ms-excel')
            response['Content-Disposition'] = 'attachment; filename="guarantees_report.xls"'

            wb = xlwt.Workbook(encoding='utf-8')
            ws = wb.add_sheet('تقرير الكفالات')

            # أنماط الخلايا
            font_style = xlwt.XFStyle()
            font_style.font.bold = True

            # عناوين الأعمدة
            columns = ['#', 'اسم المستفيد', 'المبلغ', 'المصرف', 'تاريخ البدء', 'تاريخ الانتهاء', 'الحالة']

            # كتابة العناوين
            for col_num, column_title in enumerate(columns):
                ws.write(0, col_num, column_title, font_style)

            # كتابة البيانات
            for i, guarantee in enumerate(guarantees, 1):
                row_num = i
                status = 'نشطة' if not guarantee.is_expired() else 'منتهية'
                ws.write(row_num, 0, i)
                ws.write(row_num, 1, guarantee.beneficiary_name)
                ws.write(row_num, 2, float(guarantee.amount))
                ws.write(row_num, 3, guarantee.bank_name)
                ws.write(row_num, 4, str(guarantee.start_date))
                ws.write(row_num, 5, str(guarantee.end_date))
                ws.write(row_num, 6, status)

            wb.save(response)
            return response

    # إذا لم يتم تحديد نوع التقرير بشكل صحيح
    messages.error(request, 'نوع التقرير غير صحيح.')
    return redirect('reports:index')
