{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تقارير اللجان{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تقارير اللجان</h1>
    <div>
        <a href="{% url 'reports:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى التقارير
        </a>
        <div class="btn-group">
            <a href="{% url 'reports:export' 'committees' %}?export_type=csv{% if status %}&status={{ status }}{% endif %}{% if type %}&type={{ type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-csv"></i> تصدير CSV
            </a>
            <a href="{% url 'reports:export' 'committees' %}?export_type=excel{% if status %}&status={{ status }}{% endif %}{% if type %}&type={{ type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
            <div class="card-body">
                <h5 class="card-title">إجمالي اللجان</h5>
                <p class="card-text display-4">{{ total_committees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
            <div class="card-body">
                <h5 class="card-title">اللجان النشطة</h5>
                <p class="card-text display-4">{{ active_committees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-secondary mb-3">
            <div class="card-body">
                <h5 class="card-title">اللجان المغلقة</h5>
                <p class="card-text display-4">{{ closed_committees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info mb-3">
            <div class="card-body">
                <h5 class="card-title">اللجان الممددة</h5>
                <p class="card-text display-4">{{ extended_committees }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تصفية التقرير</h5>
    </div>
    <div class="card-body">
        <form method="get" class="form-inline">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="status">الحالة</label>
                    <select name="status" id="status" class="form-control w-100">
                        <option value="">الكل</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>نشطة</option>
                        <option value="extended" {% if status == 'extended' %}selected{% endif %}>ممددة</option>
                        <option value="closed" {% if status == 'closed' %}selected{% endif %}>مغلقة</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="type">النوع</label>
                    <select name="type" id="type" class="form-control w-100">
                        <option value="">الكل</option>
                        <option value="committee" {% if type == 'committee' %}selected{% endif %}>لجنة</option>
                        <option value="council" {% if type == 'council' %}selected{% endif %}>مجلس</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" class="form-control w-100" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" class="form-control w-100" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                    <a href="{% url 'reports:committees' %}" class="btn btn-secondary">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">نتائج التقرير</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>رقم الأمر</th>
                        <th>تاريخ التشكيل</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for committee in committees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ committee.title }}</td>
                        <td>{{ committee.order_number }}</td>
                        <td>{{ committee.order_date }}</td>
                        <td>{{ committee.get_type_display }}</td>
                        <td>
                            {% if committee.status == 'active' %}
                            <span class="badge badge-success">{{ committee.get_status_display }}</span>
                            {% elif committee.status == 'extended' %}
                            <span class="badge badge-info">{{ committee.get_status_display }}</span>
                            {% else %}
                            <span class="badge badge-secondary">{{ committee.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ committee.end_date }}
                            {% if committee.is_expiring_soon and committee.status != 'closed' %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% elif committee.is_expired and committee.status != 'closed' %}
                            <span class="badge badge-danger">منتهية</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'committees:detail' committee.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا توجد لجان مطابقة لمعايير التصفية.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
