from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from .models import Committee, CommitteeMember
from .forms import CommitteeForm, CommitteeMemberForm, CommitteeExtendForm, FileUploadForm, CommitteeMemberFormSet
from core.models import File, Notification
from django.utils import timezone

@login_required
def committee_list(request):
    committees = Committee.objects.all()
    return render(request, 'committees/committee_list.html', {'committees': committees})

@login_required
def committee_detail(request, pk):
    committee = get_object_or_404(Committee, pk=pk)
    members = committee.members.all()

    # الحصول على نوع المحتوى للجنة
    committee_content_type = ContentType.objects.get_for_model(Committee)

    # الحصول على الملفات المرتبطة باللجنة
    files = File.objects.filter(
        content_type=committee_content_type,
        object_id=committee.id
    )

    return render(request, 'committees/committee_detail.html', {
        'committee': committee,
        'members': members,
        'files': files
    })

@login_required
def committee_create(request):
    if request.method == 'POST':
        form = CommitteeForm(request.POST)
        formset = CommitteeMemberFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            committee = form.save(commit=False)
            committee.created_by = request.user
            committee.save()

            # حفظ الأعضاء
            members_saved = 0
            for member_form in formset:
                if member_form.cleaned_data and not member_form.cleaned_data.get('DELETE', False):
                    # التحقق من وجود بيانات العضو
                    if member_form.cleaned_data.get('name'):
                        member = member_form.save(commit=False)
                        member.committee = committee
                        member.save()
                        members_saved += 1

            # التحقق من الحد الأدنى للأعضاء
            if members_saved < 3:
                committee.delete()  # حذف اللجنة إذا لم يتم حفظ العدد المطلوب من الأعضاء
                messages.error(request, 'يجب إضافة 3 أعضاء على الأقل.')
                return render(request, 'committees/committee_form.html', {
                    'form': form,
                    'formset': formset
                })

            messages.success(request, f'تم إنشاء اللجنة بنجاح مع {members_saved} أعضاء.')
            return redirect('committees:detail', pk=committee.pk)
        else:
            # عرض أخطاء الـ formset
            if formset.errors:
                for error in formset.errors:
                    if error:
                        messages.error(request, f'خطأ في بيانات الأعضاء: {error}')
    else:
        form = CommitteeForm()
        formset = CommitteeMemberFormSet()

    return render(request, 'committees/committee_form.html', {
        'form': form,
        'formset': formset
    })

@login_required
def committee_update(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    # منع تعديل اللجان المغلقة إلا للمشرفين
    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    # إعداد البيانات الأولية للأعضاء
    initial_data = []
    for member in committee.members.all():
        initial_data.append({
            'name': member.name,
            'role': member.role,
            'workplace': member.workplace,
            'rank': member.rank,
        })

    if request.method == 'POST':
        form = CommitteeForm(request.POST, instance=committee)
        formset = CommitteeMemberFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            form.save()

            # حذف الأعضاء الحاليين وإضافة الجدد
            committee.members.all().delete()
            for member_form in formset:
                if member_form.cleaned_data and not member_form.cleaned_data.get('DELETE', False):
                    member = member_form.save(commit=False)
                    member.committee = committee
                    member.save()

            messages.success(request, 'تم تحديث اللجنة بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeForm(instance=committee)
        formset = CommitteeMemberFormSet(initial=initial_data)

    return render(request, 'committees/committee_form.html', {
        'form': form,
        'formset': formset,
        'committee': committee
    })

@login_required
def committee_delete(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية حذف اللجان.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        committee.delete()
        messages.success(request, 'تم حذف اللجنة بنجاح.')
        return redirect('committees:list')

    return render(request, 'committees/committee_confirm_delete.html', {'committee': committee})

@login_required
def add_member(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = CommitteeMemberForm(request.POST)
        if form.is_valid():
            member = form.save(commit=False)
            member.committee = committee
            member.save()
            messages.success(request, 'تم إضافة العضو بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeMemberForm()

    return render(request, 'committees/member_form.html', {'form': form, 'committee': committee})

@login_required
def delete_member(request, pk):
    member = get_object_or_404(CommitteeMember, pk=pk)
    committee = member.committee

    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        member.delete()
        messages.success(request, 'تم حذف العضو بنجاح.')
        return redirect('committees:detail', pk=committee.pk)

    return render(request, 'committees/member_confirm_delete.html', {'member': member, 'committee': committee})

@login_required
def extend_committee(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed':
        messages.error(request, 'لا يمكن تمديد لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = CommitteeExtendForm(request.POST)
        if form.is_valid():
            days = form.cleaned_data['days']
            committee.extend(days)
            messages.success(request, f'تم تمديد اللجنة لمدة {days} يوم بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeExtendForm()

    return render(request, 'committees/committee_extend.html', {'form': form, 'committee': committee})

@login_required
def close_committee(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed':
        messages.error(request, 'اللجنة مغلقة بالفعل.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        committee.close()
        messages.success(request, 'تم إغلاق اللجنة بنجاح.')
        return redirect('committees:detail', pk=committee.pk)

    return render(request, 'committees/committee_close.html', {'committee': committee})

@login_required
def upload_document(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    # منع رفع المستندات للجان المغلقة إلا للمشرفين
    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن رفع مستندات للجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            file = form.save(commit=False)
            file.content_type = ContentType.objects.get_for_model(Committee)
            file.object_id = committee.id
            file.uploaded_by = request.user
            file.save()
            messages.success(request, 'تم رفع الملف بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = FileUploadForm()

    return render(request, 'committees/file_upload.html', {'form': form, 'committee': committee})

@login_required
def export_committees_excel(request):
    """تصدير اللجان إلى ملف Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment
    from django.http import HttpResponse
    import io

    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "اللجان والمجالس"

    # تعيين العناوين
    headers = [
        'العنوان', 'رقم الأمر الإداري', 'تاريخ التشكيل', 'النوع', 'الجهة المسؤولة',
        'المدة (بالأيام)', 'تاريخ الانتهاء', 'الحالة', 'رتبة رئيس اللجنة', 'اسم رئيس اللجنة',
        'جهة إصدار الأمر', 'رقم اضبارة الحفظ', 'تقدم الإجراءات', 'نوع المخالفة',
        'رتبة المتهم', 'مصير المجلس', 'محل التحقيق', 'آخر الإجراءات', 'ملاحظات'
    ]

    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # الحصول على البيانات
    committees = Committee.objects.all().order_by('-created_at')

    # كتابة البيانات
    for row, committee in enumerate(committees, 2):
        ws.cell(row=row, column=1, value=committee.title)
        ws.cell(row=row, column=2, value=committee.order_number)
        ws.cell(row=row, column=3, value=committee.order_date)
        ws.cell(row=row, column=4, value=committee.get_type_display())
        ws.cell(row=row, column=5, value=committee.responsible_entity or '')
        ws.cell(row=row, column=6, value=committee.duration)
        ws.cell(row=row, column=7, value=committee.end_date)
        ws.cell(row=row, column=8, value=committee.get_status_display())
        ws.cell(row=row, column=9, value=committee.chairman_rank or '')
        ws.cell(row=row, column=10, value=committee.chairman_name or '')
        ws.cell(row=row, column=11, value=committee.get_issuing_authority_display() if committee.issuing_authority else '')
        ws.cell(row=row, column=12, value=committee.file_number or '')
        ws.cell(row=row, column=13, value=committee.get_progress_status_display() if committee.progress_status else '')
        ws.cell(row=row, column=14, value=committee.violation_type or '')
        ws.cell(row=row, column=15, value=committee.accused_rank or '')
        ws.cell(row=row, column=16, value=committee.get_council_fate_display() if committee.council_fate else '')
        ws.cell(row=row, column=17, value=committee.get_investigation_location_display() if committee.investigation_location else '')
        ws.cell(row=row, column=18, value=committee.last_action or '')
        ws.cell(row=row, column=19, value=committee.notes or '')

    # تعديل عرض الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # حفظ الملف في الذاكرة
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    # إنشاء الاستجابة
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="committees_export.xlsx"'

    return response

@login_required
def import_committees_excel(request):
    """استيراد اللجان من ملف Excel"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('committees:list')

    if request.method == 'POST':
        if 'excel_file' not in request.FILES:
            messages.error(request, 'يرجى اختيار ملف Excel.')
            return render(request, 'committees/import_excel.html')

        excel_file = request.FILES['excel_file']

        # التحقق من نوع الملف
        file_extension = excel_file.name.lower().split('.')[-1]
        if file_extension not in ['xlsx', 'xls']:
            messages.error(request, 'يرجى اختيار ملف Excel صحيح (.xlsx أو .xls).')
            return render(request, 'committees/import_excel.html')

        # التحقق من حجم الملف (أقل من 10 ميجا)
        if excel_file.size > 10 * 1024 * 1024:
            messages.error(request, 'حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.')
            return render(request, 'committees/import_excel.html')

        try:
            from openpyxl import load_workbook
            from datetime import datetime

            # قراءة الملف مباشرة من الذاكرة
            try:
                # إعادة تعيين مؤشر الملف إلى البداية
                excel_file.seek(0)

                # طباعة معلومات تشخيصية
                print(f"اسم الملف: {excel_file.name}")
                print(f"حجم الملف: {excel_file.size} بايت")
                print(f"نوع المحتوى: {excel_file.content_type}")

                # قراءة الملف مباشرة
                wb = load_workbook(excel_file, read_only=True, data_only=True)
                ws = wb.active

                print(f"تم تحميل الملف بنجاح. عدد الصفوف: {ws.max_row}")

            except Exception as file_error:
                print(f"خطأ تفصيلي: {type(file_error).__name__}: {str(file_error)}")
                messages.error(request, f'خطأ في قراءة ملف Excel: {str(file_error)}. تأكد من أن الملف صحيح وغير محمي بكلمة مرور.')
                return render(request, 'committees/import_excel.html')

            imported_count = 0
            errors = []

            # التحقق من وجود بيانات في الملف
            if ws.max_row < 2:
                messages.error(request, 'الملف لا يحتوي على بيانات.')
                return render(request, 'committees/import_excel.html')

            # تخطي الصف الأول (العناوين)
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                try:
                    if not any(row):  # تخطي الصفوف الفارغة
                        continue

                    # استخراج البيانات من الصف
                    title = row[0] if row[0] else ''
                    order_number = row[1] if row[1] else ''
                    order_date = row[2] if row[2] else None
                    committee_type = row[3] if row[3] else ''
                    responsible_entity = row[4] if row[4] else ''
                    duration = row[5] if row[5] else 0
                    end_date = row[6] if row[6] else None
                    status = row[7] if row[7] else 'active'
                    chairman_rank = row[8] if row[8] else ''
                    chairman_name = row[9] if row[9] else ''
                    issuing_authority = row[10] if row[10] else ''
                    file_number = row[11] if row[11] else None
                    progress_status = row[12] if row[12] else ''
                    violation_type = row[13] if row[13] else ''
                    accused_rank = row[14] if row[14] else ''
                    council_fate = row[15] if row[15] else ''
                    investigation_location = row[16] if row[16] else ''
                    last_action = row[17] if row[17] else ''
                    notes = row[18] if row[18] else ''

                    # التحقق من البيانات المطلوبة
                    if not title or not order_number:
                        errors.append(f'الصف {row_num}: العنوان ورقم الأمر مطلوبان.')
                        continue

                    # تحويل التواريخ
                    if isinstance(order_date, str):
                        try:
                            order_date = datetime.strptime(order_date, '%Y-%m-%d').date()
                        except:
                            order_date = None

                    if isinstance(end_date, str):
                        try:
                            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                        except:
                            end_date = None

                    # تحويل النوع
                    type_mapping = {
                        'لجنة تحقيقية': 'investigation',
                        'مجلس تحقيقي': 'council',
                        'لجنة مشتركة': 'joint'
                    }
                    committee_type = type_mapping.get(committee_type, 'investigation')

                    # إنشاء اللجنة
                    committee = Committee.objects.create(
                        title=title,
                        order_number=order_number,
                        order_date=order_date,
                        type=committee_type,
                        responsible_entity=responsible_entity,
                        duration=int(duration) if duration else 30,
                        end_date=end_date,
                        chairman_rank=chairman_rank,
                        chairman_name=chairman_name,
                        file_number=int(file_number) if file_number else None,
                        violation_type=violation_type,
                        accused_rank=accused_rank,
                        last_action=last_action,
                        notes=notes,
                        created_by=request.user
                    )

                    imported_count += 1

                except Exception as e:
                    errors.append(f'الصف {row_num}: خطأ في البيانات - {str(e)}')
                    continue

            if errors:
                messages.error(request, f'تم استيراد {imported_count} لجنة بنجاح، مع وجود أخطاء في {len(errors)} صف.')
                for error in errors[:10]:  # عرض أول 10 أخطاء فقط
                    messages.warning(request, error)
            else:
                messages.success(request, f'تم استيراد {imported_count} لجنة بنجاح.')

            return redirect('committees:list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء قراءة الملف: {str(e)}')
            return render(request, 'committees/import_excel.html')

    return render(request, 'committees/import_excel.html')
