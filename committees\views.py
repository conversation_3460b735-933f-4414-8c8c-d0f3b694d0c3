from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from .models import Committee, CommitteeMember
from .forms import CommitteeForm, CommitteeMemberForm, CommitteeExtendForm, FileUploadForm, CommitteeMemberFormSet
from core.models import File, Notification
from django.utils import timezone

@login_required
def committee_list(request):
    from django.core.paginator import Paginator
    from django.db.models import Q

    # الحصول على جميع اللجان
    committees = Committee.objects.all().order_by('-created_at')

    # البحث السريع
    search_query = request.GET.get('search', '').strip()
    if search_query:
        committees = committees.filter(
            Q(order_number__icontains=search_query) |
            Q(members__name__icontains=search_query) |
            Q(members__rank__icontains=search_query)
        ).distinct()

    # الفلاتر المتقدمة
    committee_type = request.GET.get('type', '').strip()
    if committee_type:
        committees = committees.filter(type=committee_type)

    status = request.GET.get('status', '').strip()
    if status:
        committees = committees.filter(status=status)

    issuing_authority = request.GET.get('issuing_authority', '').strip()
    if issuing_authority:
        committees = committees.filter(issuing_authority=issuing_authority)

    # فلاتر التواريخ
    date_from = request.GET.get('date_from', '').strip()
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            committees = committees.filter(order_date__gte=date_from_obj)
        except ValueError:
            pass

    date_to = request.GET.get('date_to', '').strip()
    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            committees = committees.filter(order_date__lte=date_to_obj)
        except ValueError:
            pass

    end_date_from = request.GET.get('end_date_from', '').strip()
    if end_date_from:
        try:
            from datetime import datetime
            end_date_from_obj = datetime.strptime(end_date_from, '%Y-%m-%d').date()
            committees = committees.filter(end_date__gte=end_date_from_obj)
        except ValueError:
            pass

    end_date_to = request.GET.get('end_date_to', '').strip()
    if end_date_to:
        try:
            from datetime import datetime
            end_date_to_obj = datetime.strptime(end_date_to, '%Y-%m-%d').date()
            committees = committees.filter(end_date__lte=end_date_to_obj)
        except ValueError:
            pass

    # الفلاتر السريعة
    quick_filter = request.GET.get('quick_filter', '').strip()
    if quick_filter == 'expiring_soon':
        from datetime import datetime, timedelta
        today = datetime.now().date()
        week_later = today + timedelta(days=7)
        committees = committees.filter(
            end_date__gte=today,
            end_date__lte=week_later,
            status='active'
        )

    # الحصول على عدد النتائج المطلوب عرضها (افتراضي: 25)
    per_page = request.GET.get('per_page', 25)
    try:
        per_page = int(per_page)
        if per_page not in [10, 25, 50, 100]:
            per_page = 25
    except (ValueError, TypeError):
        per_page = 25

    # إعداد Pagination
    paginator = Paginator(committees, per_page)
    page_number = request.GET.get('page', 1)

    try:
        page_obj = paginator.get_page(page_number)
    except:
        page_obj = paginator.get_page(1)

    # الحصول على جهات إصدار الأمر من choices المحددة في النموذج
    available_authorities = Committee.ISSUING_AUTHORITY_CHOICES

    context = {
        'committees': page_obj,
        'page_obj': page_obj,
        'paginator': paginator,
        'per_page': per_page,
        'total_count': committees.count(),
        'search_query': search_query,
        'available_authorities': available_authorities,
    }

    return render(request, 'committees/committee_list.html', context)

@login_required
def committee_detail(request, pk):
    committee = get_object_or_404(Committee, pk=pk)
    members = committee.members.all()

    # الحصول على نوع المحتوى للجنة
    committee_content_type = ContentType.objects.get_for_model(Committee)

    # الحصول على الملفات المرتبطة باللجنة
    files = File.objects.filter(
        content_type=committee_content_type,
        object_id=committee.id
    )

    return render(request, 'committees/committee_detail.html', {
        'committee': committee,
        'members': members,
        'files': files
    })

@login_required
def committee_create(request):
    if request.method == 'POST':
        form = CommitteeForm(request.POST)
        formset = CommitteeMemberFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            committee = form.save(commit=False)
            committee.created_by = request.user
            committee.save()

            # حفظ الأعضاء
            members_saved = 0
            for member_form in formset:
                if member_form.cleaned_data and not member_form.cleaned_data.get('DELETE', False):
                    # التحقق من وجود بيانات العضو
                    if member_form.cleaned_data.get('name'):
                        member = member_form.save(commit=False)
                        member.committee = committee
                        member.save()
                        members_saved += 1

            # التحقق من الحد الأدنى للأعضاء
            if members_saved < 3:
                committee.delete()  # حذف اللجنة إذا لم يتم حفظ العدد المطلوب من الأعضاء
                messages.error(request, 'يجب إضافة 3 أعضاء على الأقل.')
                return render(request, 'committees/committee_form.html', {
                    'form': form,
                    'formset': formset
                })

            messages.success(request, f'تم إنشاء اللجنة بنجاح مع {members_saved} أعضاء.')
            return redirect('committees:detail', pk=committee.pk)
        else:
            # عرض أخطاء الـ formset
            if formset.errors:
                for error in formset.errors:
                    if error:
                        messages.error(request, f'خطأ في بيانات الأعضاء: {error}')
    else:
        form = CommitteeForm()
        formset = CommitteeMemberFormSet()

    return render(request, 'committees/committee_form.html', {
        'form': form,
        'formset': formset
    })

@login_required
def committee_update(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    # منع تعديل اللجان المغلقة إلا للمشرفين
    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    # إعداد البيانات الأولية للأعضاء
    initial_data = []
    for member in committee.members.all():
        initial_data.append({
            'name': member.name,
            'role': member.role,
            'workplace': member.workplace,
            'rank': member.rank,
        })

    if request.method == 'POST':
        form = CommitteeForm(request.POST, instance=committee)
        formset = CommitteeMemberFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            form.save()

            # حذف الأعضاء الحاليين وإضافة الجدد
            committee.members.all().delete()
            for member_form in formset:
                if member_form.cleaned_data and not member_form.cleaned_data.get('DELETE', False):
                    member = member_form.save(commit=False)
                    member.committee = committee
                    member.save()

            messages.success(request, 'تم تحديث اللجنة بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeForm(instance=committee)
        formset = CommitteeMemberFormSet(initial=initial_data)

    import json
    return render(request, 'committees/committee_form.html', {
        'form': form,
        'formset': formset,
        'committee': committee,
        'existing_members': json.dumps(initial_data),
        'is_update': True
    })

@login_required
def committee_delete(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية حذف اللجان.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        committee.delete()
        messages.success(request, 'تم حذف اللجنة بنجاح.')
        return redirect('committees:list')

    return render(request, 'committees/committee_confirm_delete.html', {'committee': committee})

@login_required
def add_member(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = CommitteeMemberForm(request.POST)
        if form.is_valid():
            member = form.save(commit=False)
            member.committee = committee
            member.save()
            messages.success(request, 'تم إضافة العضو بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeMemberForm()

    return render(request, 'committees/member_form.html', {'form': form, 'committee': committee})

@login_required
def delete_member(request, pk):
    member = get_object_or_404(CommitteeMember, pk=pk)
    committee = member.committee

    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        member.delete()
        messages.success(request, 'تم حذف العضو بنجاح.')
        return redirect('committees:detail', pk=committee.pk)

    return render(request, 'committees/member_confirm_delete.html', {'member': member, 'committee': committee})

@login_required
def extend_committee(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed':
        messages.error(request, 'لا يمكن تمديد لجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = CommitteeExtendForm(request.POST)
        if form.is_valid():
            days = form.cleaned_data['days']
            committee.extend(days)
            messages.success(request, f'تم تمديد اللجنة لمدة {days} يوم بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = CommitteeExtendForm()

    return render(request, 'committees/committee_extend.html', {'form': form, 'committee': committee})

@login_required
def close_committee(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    if committee.status == 'closed':
        messages.error(request, 'اللجنة مغلقة بالفعل.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        committee.close()
        messages.success(request, 'تم إغلاق اللجنة بنجاح.')
        return redirect('committees:detail', pk=committee.pk)

    return render(request, 'committees/committee_close.html', {'committee': committee})

@login_required
def upload_document(request, pk):
    committee = get_object_or_404(Committee, pk=pk)

    # منع رفع المستندات للجان المغلقة إلا للمشرفين
    if committee.status == 'closed' and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن رفع مستندات للجنة مغلقة.')
        return redirect('committees:detail', pk=committee.pk)

    if request.method == 'POST':
        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            file = form.save(commit=False)
            file.content_type = ContentType.objects.get_for_model(Committee)
            file.object_id = committee.id
            file.uploaded_by = request.user
            file.save()
            messages.success(request, 'تم رفع الملف بنجاح.')
            return redirect('committees:detail', pk=committee.pk)
    else:
        form = FileUploadForm()

    return render(request, 'committees/file_upload.html', {'form': form, 'committee': committee})

@login_required
def export_committees_excel(request):
    """تصدير اللجان إلى ملف Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, Alignment
    from django.http import HttpResponse
    import io

    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "اللجان والمجالس"

    # تحديد الأعمدة المرئية
    visible_columns_param = request.GET.get('visible_columns', '')
    visible_columns = visible_columns_param.split(',') if visible_columns_param else []

    # تعريف جميع الأعمدة المتاحة مع أسمائها
    all_columns = {
        'order': 'رقم الأمر الإداري',
        'date': 'تاريخ التشكيل',
        'type': 'النوع',
        'entity': 'الجهة المسؤولة',
        'duration': 'المدة (بالأيام)',
        'end-date': 'تاريخ الانتهاء',
        'status': 'الحالة',
        'chairman': 'اسم رئيس اللجنة',
        'rank': 'رتبة رئيس اللجنة',
        'authority': 'جهة إصدار الأمر',
        'file': 'رقم اضبارة الحفظ',
        'progress': 'تقدم الإجراءات',
        'violation': 'نوع المخالفة',
        'accused': 'رتبة المتهم',
        'fate': 'مصير المجلس',
        'location': 'محل التحقيق',
        'subject': 'الموضوع',
        'notes': 'ملاحظات'
    }

    # تحديد الأعمدة المطلوب تصديرها
    if visible_columns and visible_columns != ['']:
        # تصدير الأعمدة المرئية فقط
        headers = []
        column_keys = []
        for col_key in visible_columns:
            if col_key in all_columns:
                headers.append(all_columns[col_key])
                column_keys.append(col_key)
    else:
        # تصدير جميع الأعمدة (الوضع الافتراضي)
        headers = list(all_columns.values())
        column_keys = list(all_columns.keys())

    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # الحصول على البيانات مع تطبيق نفس الفلاتر المستخدمة في القائمة
    committees = Committee.objects.all().order_by('-created_at')

    # تطبيق نفس الفلاتر المستخدمة في committee_list
    from django.db.models import Q

    # البحث السريع
    search_query = request.GET.get('search', '').strip()
    if search_query:
        committees = committees.filter(
            Q(order_number__icontains=search_query) |
            Q(members__name__icontains=search_query) |
            Q(members__rank__icontains=search_query)
        ).distinct()

    # الفلاتر المتقدمة
    committee_type = request.GET.get('type', '').strip()
    if committee_type:
        committees = committees.filter(type=committee_type)

    status = request.GET.get('status', '').strip()
    if status:
        committees = committees.filter(status=status)

    issuing_authority = request.GET.get('issuing_authority', '').strip()
    if issuing_authority:
        committees = committees.filter(issuing_authority=issuing_authority)

    # فلاتر التواريخ
    from datetime import datetime
    date_from = request.GET.get('date_from', '').strip()
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            committees = committees.filter(order_date__gte=date_from_obj)
        except ValueError:
            pass

    date_to = request.GET.get('date_to', '').strip()
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            committees = committees.filter(order_date__lte=date_to_obj)
        except ValueError:
            pass

    end_date_from = request.GET.get('end_date_from', '').strip()
    if end_date_from:
        try:
            end_date_from_obj = datetime.strptime(end_date_from, '%Y-%m-%d').date()
            committees = committees.filter(end_date__gte=end_date_from_obj)
        except ValueError:
            pass

    end_date_to = request.GET.get('end_date_to', '').strip()
    if end_date_to:
        try:
            end_date_to_obj = datetime.strptime(end_date_to, '%Y-%m-%d').date()
            committees = committees.filter(end_date__lte=end_date_to_obj)
        except ValueError:
            pass

    # الفلاتر السريعة
    quick_filter = request.GET.get('quick_filter', '').strip()
    if quick_filter == 'expiring_soon':
        from datetime import timedelta
        today = datetime.now().date()
        week_later = today + timedelta(days=7)
        committees = committees.filter(
            end_date__gte=today,
            end_date__lte=week_later,
            status='active'
        )

    # دالة للحصول على قيمة العمود
    def get_column_value(committee, col_key):
        # الحصول على رئيس اللجنة
        chairman = committee.members.filter(role='chairman').first()

        column_values = {
            'order': committee.order_number,
            'date': committee.order_date,
            'type': committee.get_type_display(),
            'entity': committee.responsible_entity or '',
            'duration': committee.duration,
            'end-date': committee.end_date,
            'status': committee.get_status_display(),
            'chairman': chairman.name if chairman else '',
            'rank': chairman.rank if chairman else '',
            'authority': committee.get_issuing_authority_display() if committee.issuing_authority else '',
            'file': committee.file_number or '',
            'progress': committee.get_progress_status_display() if committee.progress_status else '',
            'violation': committee.violation_type or '',
            'accused': committee.accused_rank or '',
            'fate': committee.get_council_fate_display() if committee.council_fate else '',
            'location': committee.get_investigation_location_display() if committee.investigation_location else '',
            'subject': committee.last_action or '',
            'notes': committee.notes or ''
        }
        return column_values.get(col_key, '')

    # كتابة البيانات
    for row, committee in enumerate(committees, 2):
        for col, col_key in enumerate(column_keys, 1):
            value = get_column_value(committee, col_key)
            ws.cell(row=row, column=col, value=value)

    # تعديل عرض الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    # حفظ الملف في الذاكرة
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    # إنشاء الاستجابة
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="committees_export.xlsx"'

    return response

@login_required
def import_committees_excel(request):
    """استيراد اللجان من ملف Excel"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('committees:list')

    if request.method == 'POST':
        if 'excel_file' not in request.FILES:
            messages.error(request, 'يرجى اختيار ملف Excel.')
            return render(request, 'committees/import_excel.html')

        excel_file = request.FILES['excel_file']

        # التحقق من نوع الملف
        file_extension = excel_file.name.lower().split('.')[-1]
        if file_extension not in ['xlsx', 'xls']:
            messages.error(request, 'يرجى اختيار ملف Excel صحيح (.xlsx أو .xls).')
            return render(request, 'committees/import_excel.html')

        # التحقق من حجم الملف (أقل من 10 ميجا)
        if excel_file.size > 10 * 1024 * 1024:
            messages.error(request, 'حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 10 ميجابايت.')
            return render(request, 'committees/import_excel.html')

        try:
            from openpyxl import load_workbook
            from datetime import datetime, timedelta

            # قراءة الملف مباشرة من الذاكرة
            try:
                # إعادة تعيين مؤشر الملف إلى البداية
                excel_file.seek(0)

                # طباعة معلومات تشخيصية
                print(f"اسم الملف: {excel_file.name}")
                print(f"حجم الملف: {excel_file.size} بايت")
                print(f"نوع المحتوى: {excel_file.content_type}")

                # قراءة الملف مباشرة
                wb = load_workbook(excel_file, read_only=True, data_only=True)
                ws = wb.active

                print(f"تم تحميل الملف بنجاح. عدد الصفوف: {ws.max_row}")

            except Exception as file_error:
                print(f"خطأ تفصيلي: {type(file_error).__name__}: {str(file_error)}")
                messages.error(request, f'خطأ في قراءة ملف Excel: {str(file_error)}. تأكد من أن الملف صحيح وغير محمي بكلمة مرور.')
                return render(request, 'committees/import_excel.html')

            imported_count = 0
            errors = []
            skipped_columns = []
            missing_data_count = {
                'chairman_name': 0,
                'chairman_rank': 0,
                'issuing_authority': 0,
                'file_number': 0,
                'progress_status': 0,
                'violation_type': 0,
                'accused_rank': 0,
                'council_fate': 0,
                'investigation_location': 0,
                'last_action': 0,
                'notes': 0
            }

            # التحقق من وجود بيانات في الملف
            if ws.max_row < 2:
                messages.error(request, 'الملف لا يحتوي على بيانات.')
                return render(request, 'committees/import_excel.html')

            # تخطي الصف الأول (العناوين)
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                try:
                    if not any(row):  # تخطي الصفوف الفارغة
                        continue

                    # استخراج البيانات من الصف بطريقة آمنة (استيراد ما هو متاح)
                    def safe_get(row, index, default=''):
                        """الحصول على قيمة من الصف بطريقة آمنة"""
                        try:
                            return row[index] if len(row) > index and row[index] is not None else default
                        except (IndexError, TypeError):
                            return default

                    # البيانات الأساسية (مطلوبة)
                    order_number = safe_get(row, 0)
                    order_date = safe_get(row, 1, None)
                    committee_type = safe_get(row, 2)
                    responsible_entity = safe_get(row, 3)
                    duration = safe_get(row, 4, 30)  # قيمة افتراضية 30 يوم
                    end_date = safe_get(row, 5, None)
                    status = safe_get(row, 6, 'active')

                    # البيانات الاختيارية (يمكن أن تكون مفقودة)
                    chairman_name = safe_get(row, 7)
                    chairman_rank = safe_get(row, 8)
                    issuing_authority = safe_get(row, 9)
                    file_number = safe_get(row, 10, None)
                    progress_status = safe_get(row, 11)
                    violation_type = safe_get(row, 12)
                    accused_rank = safe_get(row, 13)
                    council_fate = safe_get(row, 14)
                    investigation_location = safe_get(row, 15)
                    last_action = safe_get(row, 16)
                    notes = safe_get(row, 17)

                    # إنشاء عنوان افتراضي
                    title = f'لجنة - {order_number}' if order_number else f'لجنة - {row_num}'

                    # التحقق من وجود بيانات أساسية (مرونة كاملة)
                    if not order_number and not committee_type:
                        errors.append(f'الصف {row_num}: الصف فارغ أو لا يحتوي على بيانات كافية.')
                        continue

                    # إنشاء قيم افتراضية للحقول الفارغة
                    if not order_number:
                        order_number = f'أمر-{row_num}-{datetime.now().strftime("%Y%m%d")}'

                    # تحويل التواريخ
                    if isinstance(order_date, str):
                        try:
                            order_date = datetime.strptime(order_date, '%Y-%m-%d').date()
                        except:
                            order_date = datetime.now().date()
                    elif order_date is None:
                        order_date = datetime.now().date()

                    # التأكد من المدة
                    try:
                        duration = int(duration) if duration else 30
                    except (ValueError, TypeError):
                        duration = 30

                    # معالجة تاريخ الانتهاء - حساب تلقائي إذا كان فارغ
                    if isinstance(end_date, str) and str(end_date).strip():
                        try:
                            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                        except:
                            # إذا فشل التحويل، احسب تلقائياً
                            end_date = order_date + timedelta(days=duration)
                    else:
                        # إذا كان فارغ، احسب تلقائياً من تاريخ التشكيل + المدة
                        end_date = order_date + timedelta(days=duration)

                    # تحويل النوع
                    type_mapping = {
                        'لجنة تحقيقية': 'investigation',
                        'مجلس تحقيقي': 'council',
                        'لجنة مشتركة': 'joint'
                    }
                    committee_type = type_mapping.get(committee_type, 'investigation')

                    # إنشاء اللجنة
                    committee = Committee.objects.create(
                        title=title,
                        order_number=order_number,
                        order_date=order_date,
                        type=committee_type,
                        responsible_entity=responsible_entity,
                        duration=duration,
                        end_date=end_date,
                        file_number=int(file_number) if file_number else None,
                        violation_type=violation_type,
                        accused_rank=accused_rank,
                        last_action=last_action,
                        notes=notes,
                        created_by=request.user
                    )

                    # إضافة رئيس اللجنة كعضو إذا كانت المعلومات متوفرة
                    if chairman_name:
                        CommitteeMember.objects.create(
                            committee=committee,
                            name=chairman_name,
                            role='chairman',
                            rank=chairman_rank or ''
                        )

                    imported_count += 1

                except Exception as e:
                    errors.append(f'الصف {row_num}: خطأ في البيانات - {str(e)}')
                    continue

            # إعداد رسائل النتائج
            total_rows = ws.max_row - 1  # عدد الصفوف بدون العناوين

            if imported_count > 0:
                messages.success(request, f'✅ تم استيراد {imported_count} لجنة بنجاح من أصل {total_rows} صف.')

                # تحديد الأعمدة المتوفرة في الملف
                if ws.max_row > 1:
                    # حساب عدد الأعمدة المتوفرة من العناوين
                    header_row = list(ws.iter_rows(min_row=1, max_row=1, values_only=True))[0]
                    available_columns = len([cell for cell in header_row if cell is not None and str(cell).strip()])
                    total_expected_columns = 18  # تم حذف عمود العنوان

                    messages.info(request, f'📊 الملف يحتوي على {available_columns} عمود.')
                    if available_columns < total_expected_columns:
                        missing_columns = total_expected_columns - available_columns
                        messages.info(request, f'ℹ️ تم استيراد البيانات المتاحة. الأعمدة المفقودة ({missing_columns}) تم ملؤها بقيم افتراضية.')

                    # إحصائيات إضافية
                    if imported_count < total_rows:
                        skipped_rows = total_rows - imported_count
                        messages.info(request, f'📋 تم تجاهل {skipped_rows} صف (فارغ أو غير صالح).')

            if errors:
                messages.warning(request, f'⚠️ تم تجاهل {len(errors)} صف بسبب مشاكل في البيانات:')
                for error in errors[:3]:  # عرض أول 3 أخطاء فقط
                    messages.error(request, f'• {error}')
                if len(errors) > 3:
                    messages.info(request, f'... و {len(errors) - 3} مشاكل أخرى.')

            if imported_count == 0:
                if total_rows == 0:
                    messages.warning(request, '⚠️ الملف لا يحتوي على بيانات.')
                else:
                    messages.warning(request, '⚠️ لم يتم استيراد أي بيانات صالحة. تحقق من محتوى الملف.')

            return redirect('committees:list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء قراءة الملف: {str(e)}')
            return render(request, 'committees/import_excel.html')

    return render(request, 'committees/import_excel.html')
