from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from datetime import timedelta

class Committee(models.Model):
    COMMITTEE_TYPE_CHOICES = (
        ('investigation', _('لجنة تحقيقية')),
        ('council', _('مجلس تحقيقي')),
        ('joint', _('لجنة مشتركة')),
    )

    COMMITTEE_STATUS_CHOICES = (
        ('active', _('نشطة')),
        ('extended', _('ممددة')),
        ('closed', _('مغلقة')),
    )

    ISSUING_AUTHORITY_CHOICES = (
        ('headquarters', _('مقر الهيأة')),
        ('safwan', _('منفذ سفوان')),
        ('sheeb', _('منفذ الشيب')),
        ('zurbatiya', _('منفذ زرباطية')),
        ('mandali', _('منفذ مندلي')),
        ('munthiriya', _('منفذ المنذرية')),
        ('rabiaa', _('منفذ ربيعة')),
        ('qaim', _('منفذ القائم')),
        ('trebil', _('منفذ طريبيل')),
        ('arar', _('منفذ عرعر')),
        ('waleed', _('منفذ الوليد')),
        ('shalamja', _('منفذ الشلامجة')),
        ('umm_qasr_north', _('ميناء ام قصر الشمال')),
        ('umm_qasr_middle', _('ميناء ام قصر الاوسط')),
        ('umm_qasr_south', _('ميناء ام قصر الجنوبي')),
        ('abu_flous', _('ميناء ابو فلوس')),
        ('khor', _('ميناء الخور')),
        ('maqal', _('ميناء المعقل')),
        ('baghdad_airport', _('مطار بغداد الدولي')),
        ('basra_airport', _('مطار البصرة الدولي')),
        ('kirkuk_airport', _('مطار كركوك الدولي')),
        ('nasiriya_airport', _('مطار الناصرية الدولي')),
        ('najaf_airport', _('مطار النجف الدولي')),
    )

    PROGRESS_STATUS_CHOICES = (
        ('completed', _('منجز')),
        ('not_completed', _('غير منجز')),
        ('under_investigation', _('قيد التحقيق')),
        ('delayed', _('متأخر')),
    )

    COUNCIL_FATE_CHOICES = (
        ('close_investigation', _('غلق التحقيق')),
        ('referral', _('احالة')),
        ('disciplinary_action', _('عقوبة انظباطية')),
    )

    title = models.CharField(_('عنوان اللجنة'), max_length=200)
    order_number = models.CharField(_('رقم الأمر الإداري'), max_length=100)
    order_date = models.DateField(_('تاريخ التشكيل'))
    type = models.CharField(_('نوع اللجنة'), max_length=20, choices=COMMITTEE_TYPE_CHOICES)
    responsible_entity = models.CharField(_('الجهة المسؤولة'), max_length=200, blank=True, null=True)
    duration = models.PositiveIntegerField(_('مدة اللجنة (بالأيام)'))
    end_date = models.DateField(_('تاريخ انتهاء اللجنة'))
    status = models.CharField(_('حالة اللجنة'), max_length=20, choices=COMMITTEE_STATUS_CHOICES, default='active')
    last_action = models.TextField(_('آخر الإجراءات'), blank=True, null=True)
    notes = models.TextField(_('ملاحظات'), blank=True, null=True)

    # الحقول الجديدة
    issuing_authority = models.CharField(_('جهة اصدار الامر الاداري'), max_length=50, choices=ISSUING_AUTHORITY_CHOICES, blank=True, null=True)
    file_number = models.IntegerField(_('رقم اضبارة الحفظ'), blank=True, null=True)
    progress_status = models.CharField(_('تقدم الاجراءات'), max_length=30, choices=PROGRESS_STATUS_CHOICES, blank=True, null=True)
    violation_type = models.CharField(_('نوع المخالفة'), max_length=200, blank=True, null=True)
    accused_rank = models.CharField(_('الرتبة/العنوان الوظيفي للمتهم'), max_length=100, blank=True, null=True)
    council_fate = models.CharField(_('مصير المجلس'), max_length=30, choices=COUNCIL_FATE_CHOICES, blank=True, null=True)
    investigation_location = models.CharField(_('محل التحقيق'), max_length=50, choices=ISSUING_AUTHORITY_CHOICES, blank=True, null=True)
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='committees', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('لجنة')
        verbose_name_plural = _('اللجان')
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    @property
    def chairman(self):
        """الحصول على رئيس اللجنة من الأعضاء"""
        return self.members.filter(role='chairman').first()

    @property
    def chairman_name(self):
        """اسم رئيس اللجنة من جدول الأعضاء"""
        chairman = self.chairman
        return chairman.name if chairman else None

    @property
    def chairman_rank(self):
        """رتبة رئيس اللجنة من جدول الأعضاء"""
        chairman = self.chairman
        return chairman.rank if chairman else None

    def is_expired(self):
        return self.end_date < timezone.now().date()

    def is_expiring_soon(self):
        return not self.is_expired() and self.end_date <= (timezone.now().date() + timedelta(days=7))

    def extend(self, days):
        self.end_date = self.end_date + timedelta(days=days)
        self.duration += days
        self.status = 'extended'
        self.save()

    def close(self):
        self.status = 'closed'
        self.save()

class CommitteeMember(models.Model):
    MEMBER_ROLE_CHOICES = (
        ('member', _('عضو')),
        ('chairman', _('رئيس اللجنة/المجلس')),
        ('rapporteur', _('مقرر')),
    )

    committee = models.ForeignKey(Committee, on_delete=models.CASCADE, related_name='members', verbose_name=_('اللجنة'))
    name = models.CharField(_('اسم العضو'), max_length=100)
    role = models.CharField(_('دور العضو'), max_length=20, choices=MEMBER_ROLE_CHOICES)
    workplace = models.CharField(_('مقر العمل'), max_length=200, blank=True, null=True)
    rank = models.CharField(_('الرتبة/العنوان الوظيفي'), max_length=100, blank=True, null=True)

    class Meta:
        verbose_name = _('عضو لجنة')
        verbose_name_plural = _('أعضاء اللجنة')
        ordering = ['role', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_role_display()})"
