from django import forms
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from .models import Department, FieldGroup, Field, DepartmentData
from .utils import DateTimeJSONEncoder, date_json_serializer
import json
import datetime

User = get_user_model()

class DepartmentForm(forms.ModelForm):
    # حقل إضافي لاسم الجدول
    table_name = forms.CharField(
        label='اسم الجدول',
        required=True,
        help_text='اسم الجدول في قاعدة البيانات (بدون مسافات أو أحرف خاصة)',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'pattern': '[a-zA-Z0-9_]+',
            'placeholder': 'مثال: employees_data'
        })
    )

    # حقل إضافي لاسم العرض
    display_name = forms.CharField(
        label='اسم العرض',
        required=False,
        help_text='اسم العرض في واجهة المستخدم',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'مثال: بيانات الموظفين'
        })
    )

    # حقول إضافية لتحديد الحقول الأولية
    initial_fields = forms.CharField(
        label='الحقول الأولية',
        required=False,
        help_text='أدخل الحقول الأولية بتنسيق JSON',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': '[{"name": "name", "display_name": "الاسم", "type": "text", "required": true}, {"name": "email", "display_name": "البريد الإلكتروني", "type": "email"}]'
        })
    )

    class Meta:
        model = Department
        fields = [
            'name', 'description', 'icon', 'color', 'display_name', 'order',
            'manager', 'access_level', 'authorized_users',
            'show_in_menu', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'icon': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'fa-folder'}),
            'color': forms.TextInput(attrs={'class': 'form-control color-picker', 'type': 'color'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'manager': forms.Select(attrs={'class': 'form-control select2'}),
            'access_level': forms.Select(attrs={'class': 'form-control', 'onchange': 'toggleAuthorizedUsers()'}),
            'authorized_users': forms.SelectMultiple(attrs={'class': 'form-control select2', 'style': 'height: 150px;'}),
            'show_in_menu': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تحديد المستخدمين المتاحين للاختيار
        self.fields['manager'].queryset = User.objects.filter(is_active=True)
        self.fields['authorized_users'].queryset = User.objects.filter(is_active=True)

        # إضافة فئات CSS للحقول
        for field_name, field in self.fields.items():
            if field_name not in ['show_in_menu', 'is_active']:
                field.widget.attrs['class'] = field.widget.attrs.get('class', '') + ' form-control'

        # إذا كان هناك قسم موجود، نملأ حقل اسم الجدول واسم العرض
        if self.instance and self.instance.pk:
            if self.instance.table_name:
                self.fields['table_name'].initial = self.instance.table_name
            if self.instance.display_name:
                self.fields['display_name'].initial = self.instance.display_name

    def clean_table_name(self):
        """التحقق من صحة اسم الجدول"""
        table_name = self.cleaned_data.get('table_name')

        # التأكد من أن اسم الجدول يحتوي على أحرف وأرقام وشرطات سفلية فقط
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', table_name):
            raise forms.ValidationError('اسم الجدول يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطات سفلية فقط.')

        # التحقق من عدم وجود قسم آخر بنفس اسم الجدول
        if Department.objects.filter(table_name=table_name).exclude(pk=self.instance.pk if self.instance.pk else None).exists():
            raise forms.ValidationError('يوجد قسم آخر بنفس اسم الجدول. الرجاء اختيار اسم آخر.')

        return table_name

    def clean_initial_fields(self):
        """التحقق من صحة الحقول الأولية"""
        initial_fields = self.cleaned_data.get('initial_fields')

        if not initial_fields:
            return []

        try:
            import json
            fields_data = json.loads(initial_fields)

            # التحقق من أن البيانات عبارة عن قائمة
            if not isinstance(fields_data, list):
                raise forms.ValidationError('الحقول الأولية يجب أن تكون قائمة.')

            # التحقق من كل حقل
            for field_data in fields_data:
                if not isinstance(field_data, dict):
                    raise forms.ValidationError('كل حقل يجب أن يكون عبارة عن كائن.')

                # التحقق من وجود الحقول الإلزامية
                if 'name' not in field_data:
                    raise forms.ValidationError('كل حقل يجب أن يحتوي على اسم (name).')

                if 'display_name' not in field_data:
                    raise forms.ValidationError('كل حقل يجب أن يحتوي على اسم العرض (display_name).')

                if 'type' not in field_data:
                    raise forms.ValidationError('كل حقل يجب أن يحتوي على نوع (type).')

                # التحقق من نوع الحقل
                field_type = field_data.get('type')
                valid_types = [choice[0] for choice in Field.FIELD_TYPES]
                if field_type not in valid_types:
                    raise forms.ValidationError(f'نوع الحقل "{field_type}" غير صالح.')

            return fields_data
        except json.JSONDecodeError:
            raise forms.ValidationError('الحقول الأولية يجب أن تكون بتنسيق JSON صالح.')
        except Exception as e:
            raise forms.ValidationError(f'خطأ في الحقول الأولية: {str(e)}')

    def save(self, commit=True):
        """حفظ القسم والحقول الأولية"""
        department = super().save(commit=False)

        # تعيين اسم الجدول واسم العرض
        department.table_name = self.cleaned_data.get('table_name')
        department.display_name = self.cleaned_data.get('display_name')

        if commit:
            department.save()
            self.save_m2m()

            # إنشاء الحقول الأولية
            initial_fields = self.cleaned_data.get('initial_fields', [])
            if initial_fields:
                for i, field_data in enumerate(initial_fields):
                    Field.objects.create(
                        department=department,
                        name=field_data.get('name'),
                        display_name=field_data.get('display_name'),
                        description=field_data.get('description', ''),
                        type=field_data.get('type'),
                        required=field_data.get('required', False),
                        default_value=field_data.get('default_value', ''),
                        placeholder=field_data.get('placeholder', ''),
                        help_text=field_data.get('help_text', ''),
                        options=field_data.get('options', {}),
                        order=i
                    )

        return department

class FieldGroupForm(forms.ModelForm):
    class Meta:
        model = FieldGroup
        fields = ['name', 'description', 'order', 'is_collapsed']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_collapsed': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class FieldForm(forms.ModelForm):
    class Meta:
        model = Field
        fields = [
            'group', 'name', 'display_name', 'description', 'type',
            'required', 'default_value', 'placeholder', 'help_text', 'options',
            'validation_type', 'validation_params', 'is_unique',
            'is_searchable', 'is_filterable', 'show_in_table', 'show_in_detail', 'show_in_export',
            'is_readonly', 'is_hidden', 'order', 'css_class', 'width'
        ]
        widgets = {
            'group': forms.Select(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'pattern': '[a-zA-Z0-9_]+'}),
            'display_name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'type': forms.Select(attrs={'class': 'form-control', 'onchange': 'updateFieldOptions()'}),
            'required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'default_value': forms.TextInput(attrs={'class': 'form-control'}),
            'placeholder': forms.TextInput(attrs={'class': 'form-control'}),
            'help_text': forms.TextInput(attrs={'class': 'form-control'}),
            'options': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '{"option1": "القيمة 1", "option2": "القيمة 2"}'
            }),
            'validation_type': forms.Select(attrs={'class': 'form-control', 'onchange': 'updateValidationParams()'}),
            'validation_params': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': '{"pattern": "^[0-9]+$", "message": "يجب إدخال أرقام فقط"}'
            }),
            'is_unique': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_searchable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_filterable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'show_in_table': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'show_in_detail': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'show_in_export': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_readonly': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_hidden': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'css_class': forms.TextInput(attrs={'class': 'form-control'}),
            'width': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '100%, 200px'}),
        }

    def save(self, commit=True):
        """تجاوز وظيفة الحفظ للتأكد من حفظ المجموعة بشكل صحيح"""
        instance = super().save(commit=False)

        # طباعة معلومات التصحيح
        print(f"Saving field with group: {self.cleaned_data.get('group')}")
        print(f"Saving field with display_name: {self.cleaned_data.get('display_name')}")

        # تعيين المجموعة بشكل صريح
        if 'group' in self.cleaned_data and self.cleaned_data['group'] is not None:
            instance.group = self.cleaned_data['group']
            print(f"Group set to: {instance.group}")

        # تعيين اسم العرض بشكل صريح
        if 'display_name' in self.cleaned_data and self.cleaned_data['display_name'] is not None:
            instance.display_name = self.cleaned_data['display_name']
            print(f"Display name set to: {instance.display_name}")

        try:
            if commit:
                instance.save()
                print(f"Field saved successfully: {instance.name}, group: {instance.group}, display_name: {instance.display_name}")

                # التحقق من أن الحقل تم حفظه بنجاح
                from departments.models import Field
                saved_field = Field.objects.filter(id=instance.id).first()
                if saved_field:
                    print(f"تم التحقق من حفظ الحقل بعد الحفظ: {saved_field.name}, المجموعة: {saved_field.group}, اسم العرض: {saved_field.display_name}")
                else:
                    print(f"تحذير: لم يتم العثور على الحقل {instance.name} بعد الحفظ!")
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في حفظ الحقل: {str(e)}")
            print(f"تفاصيل الخطأ: {error_details}")

        return instance

    def __init__(self, *args, **kwargs):
        department = kwargs.pop('department', None)
        super().__init__(*args, **kwargs)

        if department:
            # تعيين قائمة المجموعات المتاحة للقسم
            self.fields['group'].queryset = FieldGroup.objects.filter(department=department)

            # طباعة معلومات التصحيح
            print(f"Available groups for department {department.id}: {list(self.fields['group'].queryset)}")

            # تعيين المجموعة الافتراضية إذا كانت هناك مجموعة واحدة فقط
            if self.fields['group'].queryset.count() == 1:
                self.fields['group'].initial = self.fields['group'].queryset.first().id
                print(f"Setting default group: {self.fields['group'].initial}")

        # إخفاء بعض الحقول حسب نوع الحقل
        instance = kwargs.get('instance')
        if instance and instance.type not in ['select', 'multi_select', 'radio', 'checkbox']:
            self.fields['options'].widget = forms.HiddenInput()

        # طباعة معلومات التصحيح للنموذج
        print(f"Form initial data: {self.initial}")
        if 'data' in kwargs:
            print(f"Form data: {kwargs['data']}")

class DepartmentDataForm(forms.ModelForm):
    STATUS_CHOICES = (
        ('active', _('نشط')),
        ('inactive', _('غير نشط')),
        ('pending', _('قيد الانتظار')),
    )

    status = forms.ChoiceField(
        label=_('الحالة'),
        choices=STATUS_CHOICES,
        initial='active',
        widget=forms.HiddenInput(),
    )

    class Meta:
        model = DepartmentData
        fields = ['title', 'reference_number', 'status']
        widgets = {
            'title': forms.HiddenInput(),
            'reference_number': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        department = kwargs.pop('department', None)
        super().__init__(*args, **kwargs)

        self.department = department

        # تعيين قيم افتراضية للحقول المخفية
        if not self.instance.pk:
            self.initial['title'] = f'بيانات جديدة {datetime.datetime.now().strftime("%Y-%m-%d %H:%M")}'
            self.initial['reference_number'] = f'REF-{datetime.datetime.now().strftime("%Y%m%d%H%M%S")}'
            self.initial['status'] = 'active'

        # إضافة حقول ديناميكية بناءً على حقول القسم
        if department:
            fields = Field.objects.filter(department=department).order_by('group__order', 'order')

            for field in fields:
                field_name = f"field_{field.name}"

                # إنشاء الحقل المناسب حسب النوع
                if field.type == 'text':
                    self.fields[field_name] = forms.CharField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.TextInput(attrs={
                            'class': 'form-control',
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'textarea':
                    self.fields[field_name] = forms.CharField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.Textarea(attrs={
                            'class': 'form-control',
                            'rows': 3,
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'number':
                    self.fields[field_name] = forms.IntegerField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.NumberInput(attrs={
                            'class': 'form-control',
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'date':
                    self.fields[field_name] = forms.DateField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.DateInput(attrs={
                            'class': 'form-control',
                            'type': 'date',
                        })
                    )
                elif field.type == 'select':
                    choices = [(k, v) for k, v in field.get_options_dict().items()]
                    self.fields[field_name] = forms.ChoiceField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        choices=[('', '-- اختر --')] + choices,
                        widget=forms.Select(attrs={
                            'class': 'form-control',
                        })
                    )
                elif field.type == 'rich_text':
                    self.fields[field_name] = forms.CharField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.Textarea(attrs={
                            'class': 'form-control rich-text-editor',
                            'rows': 5,
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'email':
                    self.fields[field_name] = forms.EmailField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.EmailInput(attrs={
                            'class': 'form-control',
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'url':
                    self.fields[field_name] = forms.URLField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.URLInput(attrs={
                            'class': 'form-control',
                            'placeholder': field.placeholder,
                        })
                    )
                elif field.type == 'phone':
                    self.fields[field_name] = forms.CharField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.TextInput(attrs={
                            'class': 'form-control phone-input',
                            'placeholder': field.placeholder,
                            'pattern': '[0-9+\-\s()]*',
                        })
                    )
                elif field.type == 'decimal' or field.type == 'currency' or field.type == 'percentage':
                    self.fields[field_name] = forms.DecimalField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.NumberInput(attrs={
                            'class': f'form-control {field.type}-input',
                            'placeholder': field.placeholder,
                            'step': '0.01',
                        })
                    )
                elif field.type == 'time':
                    self.fields[field_name] = forms.TimeField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.TimeInput(attrs={
                            'class': 'form-control',
                            'type': 'time',
                        })
                    )
                elif field.type == 'datetime':
                    self.fields[field_name] = forms.DateTimeField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.DateTimeInput(attrs={
                            'class': 'form-control',
                            'type': 'datetime-local',
                        })
                    )
                elif field.type == 'multi_select':
                    choices = [(k, v) for k, v in field.get_options_dict().items()]
                    self.fields[field_name] = forms.MultipleChoiceField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        choices=choices,
                        widget=forms.SelectMultiple(attrs={
                            'class': 'form-control select2',
                        })
                    )
                elif field.type == 'radio':
                    choices = [(k, v) for k, v in field.get_options_dict().items()]
                    self.fields[field_name] = forms.ChoiceField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        choices=choices,
                        widget=forms.RadioSelect(attrs={
                            'class': 'form-check-input',
                        })
                    )
                elif field.type == 'checkbox':
                    self.fields[field_name] = forms.BooleanField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.CheckboxInput(attrs={
                            'class': 'form-check-input',
                        })
                    )
                elif field.type == 'file':
                    self.fields[field_name] = forms.FileField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.ClearableFileInput(attrs={
                            'class': 'form-control',
                        })
                    )
                elif field.type == 'image':
                    self.fields[field_name] = forms.ImageField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.ClearableFileInput(attrs={
                            'class': 'form-control',
                            'accept': 'image/*',
                        })
                    )
                elif field.type == 'pdf':
                    self.fields[field_name] = forms.FileField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.ClearableFileInput(attrs={
                            'class': 'form-control',
                            'accept': 'application/pdf',
                        })
                    )
                elif field.type == 'color':
                    self.fields[field_name] = forms.CharField(
                        label=field.display_name,
                        required=field.required,
                        help_text=field.help_text,
                        widget=forms.TextInput(attrs={
                            'class': 'form-control',
                            'type': 'color',
                        })
                    )

    def save(self, commit=True):
        instance = super().save(commit=False)

        if self.department:
            instance.department = self.department

        # جمع البيانات من الحقول الديناميكية
        data = {}
        files = {}

        for field_name, field_value in self.cleaned_data.items():
            if field_name.startswith('field_'):
                original_name = field_name[6:]  # إزالة "field_" من اسم الحقل

                # إضافة القيمة إلى البيانات (سيتم معالجة التواريخ لاحقًا)
                data[original_name] = field_value

                # معالجة الملفات
                if hasattr(field_value, 'name'):  # إذا كان ملف
                    files[original_name] = {
                        'name': field_value.name,
                        'size': field_value.size,
                        'content_type': field_value.content_type,
                    }

        # تحويل البيانات إلى JSON ثم مرة أخرى إلى قاموس لمعالجة التواريخ
        try:
            # استخدام المحول المخصص لتحويل التواريخ إلى سلاسل نصية
            json_data = json.dumps(data, cls=DateTimeJSONEncoder)
            # تحويل البيانات مرة أخرى إلى قاموس
            instance.data = json.loads(json_data)
        except (TypeError, ValueError) as e:
            # في حالة حدوث خطأ، نستخدم الطريقة اليدوية
            for key, value in data.items():
                if isinstance(value, (datetime.date, datetime.datetime)):
                    data[key] = value.isoformat()
            instance.data = data

        if files:
            instance.files = files

        if commit:
            instance.save()

        return instance
