#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel تجريبي بحد أدنى من البيانات لاختبار المرونة الكاملة
"""

from openpyxl import Workbook

def create_minimal_excel():
    """إنشاء ملف Excel تجريبي بحد أدنى من البيانات"""
    wb = Workbook()
    ws = wb.active
    ws.title = "اللجان والمجالس"

    # تعيين العناوين (فقط 3 أعمدة)
    headers = [
        'العنوان', 'النوع', 'الجهة المسؤولة'
    ]

    # كتابة العناوين
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)

    # إضافة بيانات تجريبية متنوعة
    test_data = [
        # صف كامل
        ['لجنة التحقيق الأولى', 'لجنة تحقيقية', 'الإدارة القانونية'],
        
        # صف بدون عنوان (سيتم إنشاء عنوان افتراضي)
        ['', 'مجلس تحقيقي', 'إدارة الشؤون الإدارية'],
        
        # صف بعنوان فقط
        ['لجنة مراجعة الإجراءات', '', ''],
        
        # صف بنوع فقط
        ['', 'لجنة مشتركة', ''],
        
        # صف فارغ تماماً (سيتم تجاهله)
        ['', '', ''],
        
        # صف بجهة مسؤولة فقط
        ['', '', 'الإدارة العامة']
    ]

    # كتابة البيانات
    for row_num, row_data in enumerate(test_data, 2):
        for col_num, value in enumerate(row_data, 1):
            ws.cell(row=row_num, column=col_num, value=value)

    # حفظ الملف
    filename = 'test_minimal_committees.xlsx'
    wb.save(filename)
    print(f'تم إنشاء ملف Excel بحد أدنى من البيانات: {filename}')
    print(f'الملف يحتوي على {len(headers)} أعمدة فقط')
    print('البيانات متنوعة لاختبار المرونة الكاملة:')
    print('- صف كامل')
    print('- صف بدون عنوان')
    print('- صف بعنوان فقط')
    print('- صف بنوع فقط')
    print('- صف فارغ تماماً')
    print('- صف بجهة مسؤولة فقط')

if __name__ == '__main__':
    create_minimal_excel()
