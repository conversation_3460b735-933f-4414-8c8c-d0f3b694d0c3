{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}
{% if department %}تعديل قسم{% else %}إضافة قسم جديد{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .nav-tabs .nav-link {
        color: #495057;
    }
    .nav-tabs .nav-link.active {
        font-weight: bold;
        color: #007bff;
    }
    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
    }
    .field-preview {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #f8f9fa;
    }
    .field-preview-item {
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #ddd;
    }
    .field-preview-item:last-child {
        border-bottom: none;
    }
    .field-preview-label {
        font-weight: bold;
        color: #555;
    }
    .field-preview-value {
        color: #007bff;
    }
    .field-type-badge {
        display: inline-block;
        padding: 2px 5px;
        font-size: 0.8rem;
        border-radius: 3px;
        background-color: #e9ecef;
        margin-right: 5px;
    }
    .required-badge {
        color: red;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if department %}تعديل قسم{% else %}إضافة قسم جديد{% endif %}</h1>
    <a href="{% url 'departments:list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة إلى قائمة الأقسام
    </a>
</div>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if department %}تعديل قسم{% else %}إضافة قسم جديد{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post" id="departmentForm">
            {% csrf_token %}

            <!-- علامات التبويب -->
            <ul class="nav nav-tabs" id="departmentTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">
                        <i class="fas fa-info-circle"></i> معلومات أساسية
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="advanced-tab" data-toggle="tab" href="#advanced" role="tab" aria-controls="advanced" aria-selected="false">
                        <i class="fas fa-cogs"></i> إعدادات متقدمة
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="fields-tab" data-toggle="tab" href="#fields" role="tab" aria-controls="fields" aria-selected="false">
                        <i class="fas fa-list-alt"></i> الحقول الأولية
                    </a>
                </li>
            </ul>

            <!-- محتوى التبويبات -->
            <div class="tab-content" id="departmentTabsContent">
                <!-- معلومات أساسية -->
                <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.table_name.id_for_label }}">{{ form.table_name.label }}</label>
                                {{ form.table_name }}
                                {% if form.table_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.table_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.table_name.help_text }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.display_name.id_for_label }}">{{ form.display_name.label }}</label>
                                {{ form.display_name }}
                                {% if form.display_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.display_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.display_name.help_text }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.icon.id_for_label }}">{{ form.icon.label }}</label>
                                {{ form.icon }}
                                {% if form.icon.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.icon.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">أدخل اسم أيقونة من Font Awesome، مثل: fa-folder</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.color.id_for_label }}">{{ form.color.label }}</label>
                                {{ form.color }}
                                {% if form.color.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.color.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="form-check">
                                    {{ form.show_in_menu }}
                                    <label class="form-check-label" for="{{ form.show_in_menu.id_for_label }}">
                                        {{ form.show_in_menu.label }}
                                    </label>
                                </div>
                                {% if form.show_in_menu.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.show_in_menu.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات متقدمة -->
                <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.order.id_for_label }}">{{ form.order.label }}</label>
                                {{ form.order }}
                                {% if form.order.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.order.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">ترتيب ظهور القسم في القائمة</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.manager.id_for_label }}">{{ form.manager.label }}</label>
                                {{ form.manager }}
                                {% if form.manager.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.manager.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.access_level.id_for_label }}">{{ form.access_level.label }}</label>
                        {{ form.access_level }}
                        {% if form.access_level.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.access_level.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="form-group" id="authorized_users_group">
                        <label for="{{ form.authorized_users.id_for_label }}">{{ form.authorized_users.label }}</label>
                        {{ form.authorized_users }}
                        {% if form.authorized_users.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.authorized_users.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اختر المستخدمين المصرح لهم بالوصول إلى هذا القسم</small>
                    </div>
                </div>

                <!-- الحقول الأولية -->
                <div class="tab-pane fade" id="fields" role="tabpanel" aria-labelledby="fields-tab">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> أدخل الحقول الأولية للقسم بتنسيق JSON. كل حقل يجب أن يحتوي على اسم (name) واسم العرض (display_name) ونوع (type).
                    </div>

                    <div class="form-group">
                        <label for="{{ form.initial_fields.id_for_label }}">{{ form.initial_fields.label }}</label>
                        {{ form.initial_fields }}
                        {% if form.initial_fields.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.initial_fields.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">{{ form.initial_fields.help_text }}</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-info" id="addTextFieldBtn">
                                <i class="fas fa-plus"></i> إضافة حقل نص
                            </button>
                            <button type="button" class="btn btn-info" id="addNumberFieldBtn">
                                <i class="fas fa-plus"></i> إضافة حقل رقم
                            </button>
                            <button type="button" class="btn btn-info" id="addDateFieldBtn">
                                <i class="fas fa-plus"></i> إضافة حقل تاريخ
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-info" id="addSelectFieldBtn">
                                <i class="fas fa-plus"></i> إضافة قائمة منسدلة
                            </button>
                            <button type="button" class="btn btn-info" id="addCheckboxFieldBtn">
                                <i class="fas fa-plus"></i> إضافة مربع اختيار
                            </button>
                        </div>
                    </div>

                    <!-- معاينة الحقول -->
                    <div class="field-preview" id="fieldPreview">
                        <h5>معاينة الحقول</h5>
                        <div id="fieldPreviewContent">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> لم يتم إضافة أي حقول بعد.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'departments:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تفعيل التبويبات
        $('#departmentTabs a').on('click', function (e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // إخفاء/إظهار المستخدمين المصرح لهم حسب مستوى الوصول
        function toggleAuthorizedUsers() {
            var accessLevel = $('#id_access_level').val();
            if (accessLevel === 'specific') {
                $('#authorized_users_group').show();
            } else {
                $('#authorized_users_group').hide();
            }
        }

        // تنفيذ الدالة عند تحميل الصفحة
        toggleAuthorizedUsers();

        // تنفيذ الدالة عند تغيير مستوى الوصول
        $('#id_access_level').change(function() {
            toggleAuthorizedUsers();
        });

        // توليد اسم الجدول واسم العرض تلقائيًا من اسم القسم
        $('#id_name').on('input', function() {
            var name = $(this).val();

            // توليد اسم الجدول
            if ($('#id_table_name').val() === '') {
                var tableName = name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '') + '_data';
                $('#id_table_name').val(tableName);
            }

            // توليد اسم العرض
            if ($('#id_display_name').val() === '') {
                var displayName = 'بيانات ' + name;
                $('#id_display_name').val(displayName);
            }
        });

        // إضافة حقل نص
        $('#addTextFieldBtn').click(function() {
            addField({
                name: 'text_field_' + Math.floor(Math.random() * 1000),
                display_name: 'حقل نص',
                type: 'text',
                required: false
            });
        });

        // إضافة حقل رقم
        $('#addNumberFieldBtn').click(function() {
            addField({
                name: 'number_field_' + Math.floor(Math.random() * 1000),
                display_name: 'حقل رقم',
                type: 'number',
                required: false
            });
        });

        // إضافة حقل تاريخ
        $('#addDateFieldBtn').click(function() {
            addField({
                name: 'date_field_' + Math.floor(Math.random() * 1000),
                display_name: 'حقل تاريخ',
                type: 'date',
                required: false
            });
        });

        // إضافة قائمة منسدلة
        $('#addSelectFieldBtn').click(function() {
            addField({
                name: 'select_field_' + Math.floor(Math.random() * 1000),
                display_name: 'قائمة منسدلة',
                type: 'select',
                required: false,
                options: {
                    'option1': 'الخيار الأول',
                    'option2': 'الخيار الثاني',
                    'option3': 'الخيار الثالث'
                }
            });
        });

        // إضافة مربع اختيار
        $('#addCheckboxFieldBtn').click(function() {
            addField({
                name: 'checkbox_field_' + Math.floor(Math.random() * 1000),
                display_name: 'مربع اختيار',
                type: 'checkbox',
                required: false
            });
        });

        // إضافة حقل إلى الحقول الأولية
        function addField(field) {
            var initialFieldsValue = $('#id_initial_fields').val();
            var fields = [];

            try {
                if (initialFieldsValue) {
                    fields = JSON.parse(initialFieldsValue);
                }
            } catch (e) {
                fields = [];
            }

            fields.push(field);
            $('#id_initial_fields').val(JSON.stringify(fields, null, 2));
            updateFieldPreview();
        }

        // تحديث معاينة الحقول
        function updateFieldPreview() {
            var initialFieldsValue = $('#id_initial_fields').val();
            var fields = [];

            try {
                if (initialFieldsValue) {
                    fields = JSON.parse(initialFieldsValue);
                }
            } catch (e) {
                fields = [];
            }

            var previewContent = '';

            if (fields.length === 0) {
                previewContent = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> لم يتم إضافة أي حقول بعد.</div>';
            } else {
                for (var i = 0; i < fields.length; i++) {
                    var field = fields[i];
                    previewContent += '<div class="field-preview-item">';
                    previewContent += '<div class="field-preview-label">' + field.display_name;
                    previewContent += ' <span class="field-type-badge">' + getFieldTypeDisplay(field.type) + '</span>';
                    if (field.required) {
                        previewContent += ' <span class="required-badge">*</span>';
                    }
                    previewContent += '</div>';
                    previewContent += '<div class="field-preview-value">اسم الحقل: ' + field.name + '</div>';
                    if (field.options) {
                        previewContent += '<div class="field-preview-value">الخيارات: ' + JSON.stringify(field.options) + '</div>';
                    }
                    previewContent += '</div>';
                }
            }

            $('#fieldPreviewContent').html(previewContent);
        }

        // الحصول على اسم العرض لنوع الحقل
        function getFieldTypeDisplay(type) {
            var typeMap = {
                'text': 'نص',
                'textarea': 'نص طويل',
                'rich_text': 'نص منسق',
                'email': 'بريد إلكتروني',
                'url': 'رابط',
                'phone': 'هاتف',
                'number': 'رقم',
                'decimal': 'رقم عشري',
                'currency': 'عملة',
                'percentage': 'نسبة مئوية',
                'date': 'تاريخ',
                'time': 'وقت',
                'datetime': 'تاريخ ووقت',
                'select': 'قائمة منسدلة',
                'multi_select': 'اختيار متعدد',
                'radio': 'زر راديو',
                'checkbox': 'مربع اختيار',
                'file': 'ملف',
                'image': 'صورة',
                'pdf': 'PDF',
                'signature': 'توقيع',
                'location': 'موقع',
                'user': 'مستخدم',
                'department': 'قسم',
                'color': 'لون'
            };

            return typeMap[type] || type;
        }

        // تحديث معاينة الحقول عند تغيير الحقول الأولية
        $('#id_initial_fields').on('input', function() {
            updateFieldPreview();
        });

        // تحديث معاينة الحقول عند تحميل الصفحة
        updateFieldPreview();
    });
</script>
{% endblock %}
