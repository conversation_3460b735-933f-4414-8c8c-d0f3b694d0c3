{% extends 'base.html' %}

{% block title %}تقرير البيانات - {{ department.name }}{% endblock %}

{% block extra_css %}
<style>
    .report-options {
        margin-bottom: 20px;
    }
    .chart-container {
        height: 400px;
        margin-bottom: 30px;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .status-active {
        color: #28a745;
    }
    .status-inactive {
        color: #dc3545;
    }
    .status-pending {
        color: #ffc107;
    }
    .field-selector {
        min-height: 150px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تقرير البيانات - {{ department.name }}</h1>
    <div>
        <a href="{% url 'departments:data_list' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة البيانات
        </a>
        <button id="printReport" class="btn btn-info">
            <i class="fas fa-print"></i> طباعة التقرير
        </button>
    </div>
</div>

<!-- خيارات التقرير -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">خيارات التقرير</h5>
    </div>
    <div class="card-body">
        <form id="reportForm" method="get" class="report-options">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="report_type">نوع التقرير:</label>
                        <select name="report_type" id="report_type" class="form-control">
                            <option value="summary" {% if report_type == 'summary' %}selected{% endif %}>ملخص</option>
                            <option value="detailed" {% if report_type == 'detailed' %}selected{% endif %}>تفصيلي</option>
                            <option value="chart" {% if report_type == 'chart' %}selected{% endif %}>رسم بياني</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="date_range">الفترة الزمنية:</label>
                        <select name="date_range" id="date_range" class="form-control">
                            <option value="all" {% if date_range == 'all' %}selected{% endif %}>جميع الفترات</option>
                            <option value="today" {% if date_range == 'today' %}selected{% endif %}>اليوم</option>
                            <option value="week" {% if date_range == 'week' %}selected{% endif %}>هذا الأسبوع</option>
                            <option value="month" {% if date_range == 'month' %}selected{% endif %}>هذا الشهر</option>
                            <option value="year" {% if date_range == 'year' %}selected{% endif %}>هذا العام</option>
                            <option value="custom" {% if date_range == 'custom' %}selected{% endif %}>فترة مخصصة</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="group_by">تجميع حسب:</label>
                        <select name="group_by" id="group_by" class="form-control">
                            <option value="none" {% if group_by == 'none' %}selected{% endif %}>بدون تجميع</option>
                            <option value="status" {% if group_by == 'status' %}selected{% endif %}>الحالة</option>
                            <option value="date" {% if group_by == 'date' %}selected{% endif %}>التاريخ</option>
                            <option value="month" {% if group_by == 'month' %}selected{% endif %}>الشهر</option>
                            <option value="user" {% if group_by == 'user' %}selected{% endif %}>المستخدم</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div id="customDateRange" class="row mt-3 {% if date_range != 'custom' %}d-none{% endif %}">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="date_from">من تاريخ:</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from }}">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="date_to">إلى تاريخ:</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to }}">
                    </div>
                </div>
            </div>
            
            <div id="chartOptions" class="row mt-3 {% if report_type != 'chart' %}d-none{% endif %}">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="chart_type">نوع الرسم البياني:</label>
                        <select name="chart_type" id="chart_type" class="form-control">
                            <option value="bar" {% if chart_type == 'bar' %}selected{% endif %}>أعمدة</option>
                            <option value="line" {% if chart_type == 'line' %}selected{% endif %}>خط</option>
                            <option value="pie" {% if chart_type == 'pie' %}selected{% endif %}>دائري</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="chart_field">حقل الرسم البياني:</label>
                        <select name="chart_field" id="chart_field" class="form-control">
                            <option value="status" {% if chart_field == 'status' %}selected{% endif %}>الحالة</option>
                            <option value="date" {% if chart_field == 'date' %}selected{% endif %}>التاريخ</option>
                            {% for field in fields %}
                            <option value="{{ field.id }}" {% if chart_field == field.id|stringformat:"i" %}selected{% endif %}>{{ field.display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div id="fieldSelector" class="row mt-3 {% if report_type != 'detailed' %}d-none{% endif %}">
                <div class="col-md-12">
                    <div class="form-group">
                        <label>اختر الحقول المراد عرضها في التقرير:</label>
                        <div class="row">
                            <div class="col-md-6">
                                <select multiple class="form-control field-selector" id="availableFields">
                                    {% for field in fields %}
                                    <option value="{{ field.id }}" {% if field.id|stringformat:"i" in selected_fields %}style="display:none"{% endif %}>{{ field.display_name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="text-center mt-2">
                                    <button type="button" class="btn btn-primary btn-sm" id="addField">
                                        <i class="fas fa-arrow-left"></i> إضافة
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <select multiple class="form-control field-selector" id="selectedFields">
                                    {% for field in fields %}
                                    {% if field.id|stringformat:"i" in selected_fields %}
                                    <option value="{{ field.id }}">{{ field.display_name }}</option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                                <div class="text-center mt-2">
                                    <button type="button" class="btn btn-danger btn-sm" id="removeField">
                                        إزالة <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                                <input type="hidden" name="selected_fields" id="selectedFieldsInput" value="{{ selected_fields|join:',' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sync"></i> تحديث التقرير
                </button>
                <a href="{% url 'departments:data_report' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </a>
                <div class="float-right">
                    <div class="btn-group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-file-export"></i> تصدير التقرير
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="{{ request.get_full_path }}&export=excel">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </a>
                            <a class="dropdown-item" href="{{ request.get_full_path }}&export=pdf">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- نتائج التقرير -->
<div class="card shadow" id="reportResults">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">نتائج التقرير</h5>
    </div>
    <div class="card-body">
        {% if report_type == 'chart' %}
        <!-- الرسم البياني -->
        <div class="chart-container">
            <canvas id="reportChart"></canvas>
        </div>
        {% endif %}
        
        {% if report_type == 'summary' %}
        <!-- ملخص التقرير -->
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white mb-4">
                    <div class="card-body">
                        <h5 class="card-title">إجمالي البيانات</h5>
                        <h2 class="card-text">{{ total_entries }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white mb-4">
                    <div class="card-body">
                        <h5 class="card-title">نشط</h5>
                        <h2 class="card-text">{{ active_entries }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white mb-4">
                    <div class="card-body">
                        <h5 class="card-title">قيد الانتظار</h5>
                        <h2 class="card-text">{{ pending_entries }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white mb-4">
                    <div class="card-body">
                        <h5 class="card-title">غير نشط</h5>
                        <h2 class="card-text">{{ inactive_entries }}</h2>
                    </div>
                </div>
            </div>
        </div>
        
        {% if group_by != 'none' %}
        <div class="table-responsive mt-4">
            <table class="table table-striped table-bordered">
                <thead class="thead-dark">
                    <tr>
                        <th>{{ group_by_label }}</th>
                        <th>عدد البيانات</th>
                        <th>النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group in grouped_data %}
                    <tr>
                        <td>{{ group.label }}</td>
                        <td>{{ group.count }}</td>
                        <td>{{ group.percentage }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        {% endif %}
        
        {% if report_type == 'detailed' %}
        <!-- تقرير تفصيلي -->
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="thead-dark">
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>الرقم المرجعي</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        {% for field in selected_field_objects %}
                        <th>{{ field.display_name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for entry in entries %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ entry.title }}</td>
                        <td>{{ entry.reference_number|default:"-" }}</td>
                        <td>
                            {% if entry.status == 'active' %}
                            <span class="status-active"><i class="fas fa-check-circle"></i> نشط</span>
                            {% elif entry.status == 'inactive' %}
                            <span class="status-inactive"><i class="fas fa-times-circle"></i> غير نشط</span>
                            {% elif entry.status == 'pending' %}
                            <span class="status-pending"><i class="fas fa-clock"></i> قيد الانتظار</span>
                            {% else %}
                            {{ entry.status }}
                            {% endif %}
                        </td>
                        <td>{{ entry.created_at|date:"Y-m-d H:i" }}</td>
                        {% for field in selected_field_objects %}
                        <td>{{ entry.get_data_value|default:"-" }}</td>
                        {% endfor %}
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="{{ 5|add:selected_field_objects|length }}" class="text-center">لا توجد بيانات متاحة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        
        {% if not entries and report_type != 'chart' %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد بيانات متاحة للمعايير المحددة.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // تحديث خيارات التقرير حسب النوع المحدد
        $('#report_type').change(function() {
            var reportType = $(this).val();
            
            if (reportType === 'chart') {
                $('#chartOptions').removeClass('d-none');
            } else {
                $('#chartOptions').addClass('d-none');
            }
            
            if (reportType === 'detailed') {
                $('#fieldSelector').removeClass('d-none');
            } else {
                $('#fieldSelector').addClass('d-none');
            }
        });
        
        // تحديث خيارات التاريخ المخصص
        $('#date_range').change(function() {
            var dateRange = $(this).val();
            
            if (dateRange === 'custom') {
                $('#customDateRange').removeClass('d-none');
            } else {
                $('#customDateRange').addClass('d-none');
            }
        });
        
        // إضافة وإزالة الحقول المحددة
        $('#addField').click(function(e) {
            e.preventDefault();
            $('#availableFields option:selected').each(function() {
                $(this).hide();
                $('#selectedFields').append($(this).clone().show());
            });
            updateSelectedFields();
        });
        
        $('#removeField').click(function(e) {
            e.preventDefault();
            $('#selectedFields option:selected').each(function() {
                var fieldId = $(this).val();
                $('#availableFields option[value="' + fieldId + '"]').show();
                $(this).remove();
            });
            updateSelectedFields();
        });
        
        function updateSelectedFields() {
            var selectedFields = [];
            $('#selectedFields option').each(function() {
                selectedFields.push($(this).val());
            });
            $('#selectedFieldsInput').val(selectedFields.join(','));
        }
        
        // طباعة التقرير
        $('#printReport').click(function() {
            window.print();
        });
        
        {% if report_type == 'chart' and chart_data %}
        // إنشاء الرسم البياني
        var ctx = document.getElementById('reportChart').getContext('2d');
        var reportChart = new Chart(ctx, {
            type: '{{ chart_type }}',
            data: {
                labels: {{ chart_labels|safe }},
                datasets: [{
                    label: '{{ chart_title }}',
                    data: {{ chart_values|safe }},
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)',
                        'rgba(255, 159, 64, 0.5)',
                        'rgba(199, 199, 199, 0.5)',
                        'rgba(83, 102, 255, 0.5)',
                        'rgba(40, 159, 64, 0.5)',
                        'rgba(210, 199, 199, 0.5)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)',
                        'rgba(83, 102, 255, 1)',
                        'rgba(40, 159, 64, 1)',
                        'rgba(210, 199, 199, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ chart_title }}',
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        display: '{{ chart_type }}' !== 'pie'
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
