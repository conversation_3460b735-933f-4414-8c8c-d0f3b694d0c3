from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db import transaction
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.utils import timezone
import json
import csv
import io
# سيتم استخدام openpyxl لاحقًا
# import openpyxl
# from openpyxl.styles import Font, Alignment, PatternFill

from .models import Department, FieldGroup, Field, DepartmentData, DepartmentDataHistory
from .forms import DepartmentForm, FieldGroupForm, FieldForm, DepartmentDataForm

@login_required
def department_list(request):
    # عرض جميع الأقسام للمشرفين، والأقسام المصرح بها للمستخدمين العاديين
    if hasattr(request.user, 'user_type') and request.user.user_type == 'admin':
        departments = Department.objects.all()
    else:
        # الحصول على الأقسام التي يمكن للمستخدم الوصول إليها
        departments = Department.objects.filter(is_active=True)
        departments = [dept for dept in departments if dept.can_user_access(request.user)]

    return render(request, 'departments/department_list.html', {'departments': departments})

@login_required
def department_detail(request, pk):
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    fields = department.fields.all().order_by('group__order', 'order')
    field_groups = department.field_groups.all().order_by('order')

    return render(request, 'departments/department_detail.html', {
        'department': department,
        'fields': fields,
        'field_groups': field_groups
    })

@login_required
def department_create(request):
    # التحقق من صلاحية الوصول (فقط المشرفين يمكنهم إنشاء أقسام جديدة)
    if hasattr(request.user, 'user_type') and request.user.user_type == 'admin':
        pass  # المستخدم مشرف، يمكنه المتابعة
    else:
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة. فقط المشرفين يمكنهم إنشاء أقسام جديدة.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            # حفظ القسم والحقول الأولية
            department = form.save()

            # إنشاء جدول البيانات في قاعدة البيانات
            try:
                # استخدام الوظيفة المحسنة لإنشاء الجدول
                from .utils import create_department_table, add_field_to_table

                # إنشاء الجدول
                table_created = create_department_table(department.table_name)

                if table_created:
                    messages.success(request, f'تم إنشاء الجدول {department.table_name} بنجاح.')
                else:
                    messages.warning(request, f'الجدول {department.table_name} موجود بالفعل. سيتم استخدام الجدول الموجود.')

                # إضافة أعمدة للحقول الأولية
                fields = Field.objects.filter(department=department)

                if fields.exists():
                    for field in fields:
                        try:
                            field_added = add_field_to_table(
                                department.table_name,
                                field.name,
                                field.type,
                                field.required
                            )

                            if field_added:
                                print(f"تم إضافة الحقل {field.name} بنجاح")
                            else:
                                print(f"الحقل {field.name} موجود بالفعل")
                        except Exception as column_error:
                            print(f"خطأ في إضافة الحقل {field.name}: {str(column_error)}")
                            # نستمر في إضافة الحقول الأخرى حتى لو فشلت إضافة حقل واحد
                            continue

                messages.success(request, 'تم إنشاء القسم وجدول البيانات بنجاح.')
            except Exception as e:
                import traceback
                import logging
                logger = logging.getLogger(__name__)
                error_details = traceback.format_exc()

                # تسجيل الخطأ في وحدة التحكم
                logger.error(f"خطأ في إنشاء جدول البيانات: {str(e)}")
                logger.error(f"تفاصيل الخطأ: {error_details}")

                # عرض رسالة للمستخدم
                messages.error(request, f'تم إنشاء القسم ولكن حدث خطأ أثناء إنشاء جدول البيانات: {str(e)}')

                # طباعة تفاصيل الخطأ في وحدة التحكم
                print(f"ERROR: {str(e)}")
                print(f"TRACEBACK: {error_details}")

            return redirect('departments:detail', pk=department.pk)
    else:
        form = DepartmentForm()

    return render(request, 'departments/department_form.html', {'form': form})

@login_required
def department_update(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث القسم بنجاح.')
            return redirect('departments:detail', pk=department.pk)
    else:
        form = DepartmentForm(instance=department)

    return render(request, 'departments/department_form.html', {'form': form, 'department': department})

@login_required
def department_delete(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        # حذف جدول البيانات
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute(f"DROP TABLE IF EXISTS {department.table_name}")

        department.delete()
        messages.success(request, 'تم حذف القسم بنجاح.')
        return redirect('departments:list')

    return render(request, 'departments/department_confirm_delete.html', {'department': department})

@login_required
def add_field(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        # تعديل البيانات المرسلة للتأكد من وجود جميع الحقول المطلوبة
        post_data = request.POST.copy()
        if 'validation_type' not in post_data:
            post_data['validation_type'] = 'none'  # تعيين قيمة افتراضية لنوع التحقق

        form = FieldForm(post_data, department=department)
        if form.is_valid():
            field = form.save(commit=False)
            field.department = department

            # التحقق من صحة الخيارات إذا كان الحقل من نوع قائمة منسدلة
            if field.type in ['select', 'multi_select', 'radio', 'checkbox'] and field.options:
                try:
                    if isinstance(field.options, str):
                        json.loads(field.options)
                except json.JSONDecodeError:
                    messages.error(request, 'الخيارات غير صالحة. يجب أن تكون بتنسيق JSON.')
                    return render(request, 'departments/field_form.html', {'form': form, 'department': department})

            # حفظ الحقل مع المجموعة
            try:
                field.save()
                print(f"تم حفظ الحقل: {field.name}, المجموعة: {field.group}")
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"خطأ في حفظ الحقل: {str(e)}")
                print(f"تفاصيل الخطأ: {error_details}")
                messages.error(request, f'حدث خطأ أثناء حفظ الحقل: {str(e)}')
                return render(request, 'departments/field_form.html', {'form': form, 'department': department})

            # إضافة الحقل إلى جدول البيانات إذا كان للقسم اسم جدول
            if hasattr(department, 'table_name') and department.table_name:
                try:
                    # استخدام الوظيفة المحسنة لإضافة الحقل إلى الجدول
                    from .utils import add_field_to_table

                    field_added = add_field_to_table(
                        department.table_name,
                        field.name,
                        field.type,
                        field.required
                    )

                    if field_added:
                        print(f"تم إضافة الحقل {field.name} إلى الجدول {department.table_name} بنجاح")
                    else:
                        print(f"الحقل {field.name} موجود بالفعل في الجدول {department.table_name}")
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    print(f"خطأ في إضافة الحقل {field.name} إلى الجدول: {str(e)}")
                    print(f"تفاصيل الخطأ: {error_details}")
                    messages.error(request, f'حدث خطأ أثناء إضافة الحقل إلى قاعدة البيانات: {str(e)}')

            messages.success(request, 'تم إضافة الحقل بنجاح.')
            return redirect('departments:detail', pk=department.pk)
    else:
        form = FieldForm(department=department)

    return render(request, 'departments/field_form.html', {'form': form, 'department': department})

@login_required
def update_field(request, pk):
    """تعديل حقل"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    field = get_object_or_404(Field, pk=pk)
    department = field.department
    old_name = field.name
    old_type = field.type
    old_required = field.required
    old_display_name = field.display_name  # حفظ اسم العرض القديم للتصحيح

    if request.method == 'POST':
        # طباعة البيانات المرسلة من النموذج
        print(f"POST data: {request.POST}")

        # تحديث الحقل مباشرة بدلاً من استخدام النموذج
        display_name = request.POST.get('display_name')
        if not display_name:
            display_name = request.POST.get('display_name_hidden')

        if not display_name:
            display_name = old_display_name

        print(f"Using display_name: {display_name}")

        # تحديث الحقول الأخرى من النموذج
        name = request.POST.get('name', field.name)
        description = request.POST.get('description', field.description)
        field_type = request.POST.get('type', field.type)
        required = 'required' in request.POST
        group_id = request.POST.get('group', None)
        group = None
        if group_id and group_id != 'None':
            group = FieldGroup.objects.filter(id=group_id, department=department).first()

        # تحديث الحقل مباشرة في قاعدة البيانات
        try:
            # تحديث الحقل في الذاكرة
            field.name = name
            field.display_name = display_name
            field.description = description
            field.type = field_type
            field.required = required
            field.group = group

            # حفظ التغييرات
            field.save()

            print(f"Field updated: name={field.name}, display_name={field.display_name}, type={field.type}, required={field.required}, group={field.group}")

            # تحديث الحقل مباشرة في قاعدة البيانات للتأكد من حفظ اسم العرض
            from django.db import connection
            cursor = connection.cursor()
            cursor.execute(
                "UPDATE departments_field SET display_name = %s WHERE id = %s",
                [display_name, field.id]
            )
            print(f"Updated display_name directly in database to: {display_name}")

            # تحديث الحقل في جدول البيانات إذا تغير الاسم أو النوع أو الإلزامية
            if hasattr(department, 'table_name') and department.table_name and (old_name != name or old_type != field_type or old_required != required):
                try:
                    from django.db import connection
                    cursor = connection.cursor()

                    # تحديد نوع قاعدة البيانات
                    db_engine = connection.vendor

                    field_type_map = {
                        'text': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'textarea': 'TEXT',
                        'rich_text': 'TEXT',
                        'email': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'url': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'phone': 'VARCHAR(50)' if db_engine != 'sqlite' else 'TEXT',
                        'number': 'INT' if db_engine != 'sqlite' else 'INTEGER',
                        'decimal': 'DECIMAL(15, 2)' if db_engine != 'sqlite' else 'REAL',
                        'currency': 'DECIMAL(15, 2)' if db_engine != 'sqlite' else 'REAL',
                        'percentage': 'DECIMAL(5, 2)' if db_engine != 'sqlite' else 'REAL',
                        'date': 'DATE',
                        'time': 'TIME',
                        'datetime': 'DATETIME',
                        'select': 'VARCHAR(100)' if db_engine != 'sqlite' else 'TEXT',
                        'multi_select': 'TEXT',
                        'radio': 'VARCHAR(100)' if db_engine != 'sqlite' else 'TEXT',
                        'checkbox': 'BOOLEAN' if db_engine != 'sqlite' else 'INTEGER',
                        'file': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'image': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'pdf': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'signature': 'TEXT',
                        'location': 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT',
                        'user': 'INT' if db_engine != 'sqlite' else 'INTEGER',
                        'department': 'INT' if db_engine != 'sqlite' else 'INTEGER',
                        'color': 'VARCHAR(20)' if db_engine != 'sqlite' else 'TEXT',
                    }

                    sql_type = field_type_map.get(field_type, 'VARCHAR(255)' if db_engine != 'sqlite' else 'TEXT')
                    nullable = 'NULL' if not required else 'NOT NULL'

                    # إذا تغير اسم الحقل، نقوم بإنشاء حقل جديد ونسخ البيانات ثم حذف الحقل القديم
                    if old_name != name:
                        # SQLite لا يدعم حذف الأعمدة بشكل مباشر، لذلك نحتاج إلى طريقة مختلفة
                        if db_engine == 'sqlite':
                            # 1. إنشاء جدول مؤقت بالهيكل الجديد
                            # 2. نسخ البيانات من الجدول القديم إلى الجدول المؤقت
                            # 3. حذف الجدول القديم
                            # 4. إعادة تسمية الجدول المؤقت

                            # إنشاء الحقل الجديد
                            cursor.execute(f"ALTER TABLE {department.table_name} ADD COLUMN {name} {sql_type} {nullable}")

                            # نسخ البيانات من الحقل القديم إلى الحقل الجديد
                            cursor.execute(f"UPDATE {department.table_name} SET {name} = {old_name}")

                            # لا يمكن حذف العمود في SQLite، لكن يمكننا تجاهله في الاستعلامات المستقبلية
                            print(f"تم إضافة العمود الجديد {name} ونسخ البيانات من {old_name}")
                            messages.info(request, f'تم إضافة الحقل الجديد. لا يمكن حذف الحقل القديم في SQLite، لكن سيتم تجاهله.')
                        else:
                            # إنشاء الحقل الجديد
                            cursor.execute(f"ALTER TABLE {department.table_name} ADD COLUMN {name} {sql_type} {nullable}")

                            # نسخ البيانات من الحقل القديم إلى الحقل الجديد
                            cursor.execute(f"UPDATE {department.table_name} SET {name} = {old_name}")

                            # حذف الحقل القديم
                            cursor.execute(f"ALTER TABLE {department.table_name} DROP COLUMN {old_name}")
                    else:
                        # تعديل نوع الحقل فقط
                        if db_engine == 'sqlite':
                            # SQLite لا يدعم تعديل نوع العمود، لذلك نقوم بإنشاء جدول جديد ونسخ البيانات
                            messages.info(request, f'تم تحديث خصائص الحقل. لا يمكن تغيير نوع الحقل في SQLite، لكن سيتم تطبيق التغييرات على البيانات الجديدة.')
                        else:
                            cursor.execute(f"ALTER TABLE {department.table_name} MODIFY COLUMN {name} {sql_type} {nullable}")
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    print(f"خطأ في تحديث الحقل {name}: {str(e)}")
                    print(f"تفاصيل الخطأ: {error_details}")
                    messages.error(request, f'حدث خطأ أثناء تحديث الحقل في قاعدة البيانات: {str(e)}')

            # تحديث الحقل مرة أخرى للتأكد من حفظ جميع التغييرات
            field.refresh_from_db()
            print(f"Final display name after refresh: {field.display_name}")

            # تحديث مرة أخرى إذا لم يتم حفظ اسم العرض
            if field.display_name != display_name:
                print(f"Display name not updated correctly. Updating again...")
                field.display_name = display_name
                field.save(update_fields=['display_name'])

                # تحديث مباشر في قاعدة البيانات
                cursor = connection.cursor()
                cursor.execute(
                    "UPDATE departments_field SET display_name = %s WHERE id = %s",
                    [display_name, field.id]
                )

                field.refresh_from_db()
                print(f"Final display name after second update: {field.display_name}")

            messages.success(request, 'تم تحديث الحقل بنجاح.')
            return redirect('departments:detail', pk=department.pk)
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في تحديث الحقل: {str(e)}")
            print(f"تفاصيل الخطأ: {error_details}")
            messages.error(request, f'حدث خطأ أثناء تحديث الحقل: {str(e)}')
    else:
        form = FieldForm(instance=field, department=department)

    return render(request, 'departments/field_form.html', {'form': form, 'department': department, 'field': field})

@login_required
def delete_field(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    field = get_object_or_404(Field, pk=pk)
    department = field.department

    if request.method == 'POST':
        # حذف الحقل من جدول البيانات إذا كان للقسم اسم جدول
        if hasattr(department, 'table_name') and department.table_name:
            try:
                from django.db import connection
                cursor = connection.cursor()
                db_engine = connection.vendor

                if db_engine == 'sqlite':
                    # SQLite لا يدعم حذف الأعمدة بشكل مباشر
                    # لذلك نقوم بتجاهل العمود في الاستعلامات المستقبلية
                    messages.info(request, 'لا يمكن حذف الحقل من جدول البيانات في SQLite، لكن سيتم تجاهله في الاستعلامات المستقبلية.')
                else:
                    # حذف العمود في قواعد البيانات الأخرى
                    cursor.execute(f"ALTER TABLE {department.table_name} DROP COLUMN {field.name}")
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"خطأ في حذف العمود {field.name}: {str(e)}")
                print(f"تفاصيل الخطأ: {error_details}")
                messages.error(request, f'حدث خطأ أثناء حذف الحقل من قاعدة البيانات: {str(e)}')

        field.delete()
        messages.success(request, 'تم حذف الحقل بنجاح.')
        return redirect('departments:detail', pk=department.pk)

    return render(request, 'departments/field_confirm_delete.html', {'field': field, 'department': department})

@login_required
def add_field_group(request, pk):
    """إضافة مجموعة حقول جديدة للقسم"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)

    if request.method == 'POST':
        form = FieldGroupForm(request.POST)
        if form.is_valid():
            group = form.save(commit=False)
            group.department = department
            group.save()
            messages.success(request, 'تم إضافة مجموعة الحقول بنجاح.')
            return redirect('departments:detail', pk=department.pk)
    else:
        form = FieldGroupForm()

    return render(request, 'departments/field_group_form.html', {'form': form, 'department': department})

@login_required
def add_field_to_group(request, pk, group_id):
    """إضافة حقل جديد إلى مجموعة محددة"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)
    group = get_object_or_404(FieldGroup, pk=group_id, department=department)

    if request.method == 'POST':
        # تعديل البيانات المرسلة للتأكد من وجود جميع الحقول المطلوبة
        post_data = request.POST.copy()
        if 'validation_type' not in post_data:
            post_data['validation_type'] = 'none'  # تعيين قيمة افتراضية لنوع التحقق

        form = FieldForm(post_data, department=department)
        if form.is_valid():
            field = form.save(commit=False)
            field.department = department
            field.group = group  # تعيين المجموعة بشكل صريح

            # التحقق من صحة الخيارات إذا كان الحقل من نوع قائمة منسدلة
            if field.type in ['select', 'multi_select', 'radio', 'checkbox'] and field.options:
                try:
                    if isinstance(field.options, str):
                        json.loads(field.options)
                except json.JSONDecodeError:
                    messages.error(request, 'الخيارات غير صالحة. يجب أن تكون بتنسيق JSON.')
                    return render(request, 'departments/field_form.html', {'form': form, 'department': department, 'group': group})

            # حفظ الحقل مع المجموعة
            try:
                field.save()
                print(f"تم حفظ الحقل: {field.name}, المجموعة: {field.group}")
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"خطأ في حفظ الحقل: {str(e)}")
                print(f"تفاصيل الخطأ: {error_details}")
                messages.error(request, f'حدث خطأ أثناء حفظ الحقل: {str(e)}')
                return render(request, 'departments/field_form.html', {'form': form, 'department': department, 'group': group})

            # إضافة الحقل إلى جدول البيانات إذا كان للقسم اسم جدول
            if hasattr(department, 'table_name') and department.table_name:
                try:
                    # استخدام الوظيفة المحسنة لإضافة الحقل إلى الجدول
                    from .utils import add_field_to_table

                    field_added = add_field_to_table(
                        department.table_name,
                        field.name,
                        field.type,
                        field.required
                    )

                    if field_added:
                        print(f"تم إضافة الحقل {field.name} إلى الجدول {department.table_name} بنجاح")
                    else:
                        print(f"الحقل {field.name} موجود بالفعل في الجدول {department.table_name}")
                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    print(f"خطأ في إضافة الحقل {field.name} إلى الجدول: {str(e)}")
                    print(f"تفاصيل الخطأ: {error_details}")
                    messages.error(request, f'حدث خطأ أثناء إضافة الحقل إلى قاعدة البيانات: {str(e)}')

            messages.success(request, 'تم إضافة الحقل بنجاح.')
            return redirect('departments:detail', pk=department.pk)
    else:
        # تعيين المجموعة الافتراضية في النموذج
        form = FieldForm(department=department, initial={'group': group.id})

    return render(request, 'departments/field_form.html', {'form': form, 'department': department, 'group': group})

@login_required
def update_field_group(request, pk):
    """تعديل مجموعة حقول"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    group = get_object_or_404(FieldGroup, pk=pk)
    department = group.department

    if request.method == 'POST':
        form = FieldGroupForm(request.POST, instance=group)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث مجموعة الحقول بنجاح.')
            return redirect('departments:detail', pk=department.pk)
    else:
        form = FieldGroupForm(instance=group)

    return render(request, 'departments/field_group_form.html', {'form': form, 'department': department, 'group': group})

@login_required
def delete_field_group(request, pk):
    """حذف مجموعة حقول"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    group = get_object_or_404(FieldGroup, pk=pk)
    department = group.department

    if request.method == 'POST':
        # تحديث الحقول المرتبطة بهذه المجموعة
        Field.objects.filter(group=group).update(group=None)

        group.delete()
        messages.success(request, 'تم حذف مجموعة الحقول بنجاح.')
        return redirect('departments:detail', pk=department.pk)

    return render(request, 'departments/field_group_confirm_delete.html', {'group': group, 'department': department})

@login_required
def department_data_list(request, pk):
    """عرض قائمة بيانات القسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    # الحصول على البيانات مع إمكانية التصفية والبحث
    data_entries = DepartmentData.objects.filter(department=department)

    # البحث المتقدم
    search_query = request.GET.get('search', '')
    if search_query:
        # البحث في العنوان والرقم المرجعي والبيانات
        from django.db.models import Q
        data_entries = data_entries.filter(
            Q(title__icontains=search_query) |
            Q(reference_number__icontains=search_query) |
            Q(data__icontains=search_query)
        )

    # التصفية حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        data_entries = data_entries.filter(status=status_filter)

    # تصفية حسب التاريخ
    date_from = request.GET.get('date_from', '')
    date_from_obj = None
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            data_entries = data_entries.filter(created_at__gte=date_from_obj)
        except ValueError:
            pass

    date_to = request.GET.get('date_to', '')
    date_to_obj = None
    if date_to:
        try:
            from datetime import datetime, timedelta
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # إضافة يوم كامل للتاريخ النهائي
            date_to_obj_end = date_to_obj + timedelta(days=1)
            data_entries = data_entries.filter(created_at__lt=date_to_obj_end)
        except ValueError:
            pass

    # تصفية حسب المستخدم
    user_filter = request.GET.get('user', '')
    if user_filter:
        data_entries = data_entries.filter(created_by_id=user_filter)

    # تصفية حسب الحقول المخصصة
    for field in Field.objects.filter(department=department, is_filterable=True):
        field_filter = request.GET.get(f'field_{field.id}', '')
        if field_filter:
            # بناء استعلام للبحث في البيانات المخصصة
            filter_query = {f'data__{field.name}__icontains': field_filter}
            data_entries = data_entries.filter(**filter_query)

    # الترتيب
    sort_by = request.GET.get('sort', '-created_at')
    data_entries = data_entries.order_by(sort_by)

    # التقسيم إلى صفحات
    items_per_page = int(request.GET.get('items_per_page', 10))  # عدد العناصر في كل صفحة
    paginator = Paginator(data_entries, items_per_page)
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    # الحصول على الحقول القابلة للبحث والتصفية
    searchable_fields = Field.objects.filter(department=department, is_searchable=True)
    filterable_fields = Field.objects.filter(department=department, is_filterable=True)

    # الحصول على الحقول المراد عرضها في الجدول
    table_fields = Field.objects.filter(department=department, show_in_table=True).order_by('order')

    # الحصول على قائمة المستخدمين للتصفية
    from django.contrib.auth import get_user_model
    User = get_user_model()
    users = User.objects.filter(
        id__in=DepartmentData.objects.filter(department=department).values_list('created_by_id', flat=True).distinct()
    )

    context = {
        'department': department,
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'user_filter': user_filter,
        'sort_by': sort_by,
        'items_per_page': items_per_page,
        'searchable_fields': searchable_fields,
        'filterable_fields': filterable_fields,
        'table_fields': table_fields,
        'users': users,
    }

    return render(request, 'departments/department_data_list.html', context)

@login_required
def department_data_create(request, pk):
    """إنشاء بيانات جديدة للقسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = DepartmentDataForm(request.POST, request.FILES, department=department)
        if form.is_valid():
            data_entry = form.save(commit=False)
            data_entry.department = department
            data_entry.created_by = request.user
            data_entry.updated_by = request.user
            data_entry.save()

            # إنشاء سجل تاريخ
            DepartmentDataHistory.objects.create(
                data_entry=data_entry,
                data_snapshot=data_entry.data,
                action='create',
                user=request.user
            )

            messages.success(request, 'تم إنشاء البيانات بنجاح.')
            return redirect('departments:data_detail', pk=data_entry.pk)
    else:
        form = DepartmentDataForm(department=department)

    context = {
        'department': department,
        'form': form,
    }

    return render(request, 'departments/department_data_form.html', context)

@login_required
def department_data_detail(request, pk):
    """عرض تفاصيل بيانات القسم"""
    data_entry = get_object_or_404(DepartmentData, pk=pk)
    department = data_entry.department

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    # الحصول على الحقول المراد عرضها في التفاصيل
    fields = Field.objects.filter(department=department, show_in_detail=True).order_by('group__order', 'order')

    # الحصول على سجل التاريخ
    history = data_entry.history.all().order_by('-timestamp')

    context = {
        'data_entry': data_entry,
        'department': department,
        'fields': fields,
        'history': history,
    }

    return render(request, 'departments/department_data_detail.html', context)

@login_required
def department_data_update(request, pk):
    """تحديث بيانات القسم"""
    data_entry = get_object_or_404(DepartmentData, pk=pk)
    department = data_entry.department

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = DepartmentDataForm(request.POST, request.FILES, instance=data_entry, department=department)
        if form.is_valid():
            # حفظ نسخة من البيانات القديمة للتاريخ
            old_data = data_entry.data.copy() if data_entry.data else {}

            # حفظ البيانات الجديدة
            data_entry = form.save(commit=False)
            data_entry.updated_by = request.user
            data_entry.save()

            # إنشاء سجل تاريخ
            DepartmentDataHistory.objects.create(
                data_entry=data_entry,
                data_snapshot=old_data,
                action='update',
                user=request.user
            )

            messages.success(request, 'تم تحديث البيانات بنجاح.')
            return redirect('departments:data_detail', pk=data_entry.pk)
    else:
        # تعبئة النموذج بالبيانات الحالية
        initial_data = {}
        if data_entry.data:
            for field_name, field_value in data_entry.data.items():
                initial_data[f'field_{field_name}'] = field_value

        form = DepartmentDataForm(instance=data_entry, department=department, initial=initial_data)

    context = {
        'form': form,
        'department': department,
        'data_entry': data_entry,
    }

    return render(request, 'departments/department_data_form.html', context)

@login_required
def department_data_delete(request, pk):
    """حذف بيانات القسم"""
    data_entry = get_object_or_404(DepartmentData, pk=pk)
    department = data_entry.department

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    if request.method == 'POST':
        # إنشاء سجل تاريخ قبل الحذف
        DepartmentDataHistory.objects.create(
            data_entry=data_entry,
            data_snapshot=data_entry.data,
            action='delete',
            user=request.user
        )

        data_entry.delete()
        messages.success(request, 'تم حذف البيانات بنجاح.')
        return redirect('departments:data_list', pk=department.pk)

    context = {
        'data_entry': data_entry,
        'department': department,
    }

    return render(request, 'departments/department_data_confirm_delete.html', context)

@login_required
def department_data_export(request, pk):
    """تصدير بيانات القسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    # الحصول على البيانات
    data_entries = DepartmentData.objects.filter(department=department)

    # التصفية حسب المعايير
    search_query = request.GET.get('search', '')
    if search_query:
        # البحث في العنوان والرقم المرجعي والبيانات
        from django.db.models import Q
        data_entries = data_entries.filter(
            Q(title__icontains=search_query) |
            Q(reference_number__icontains=search_query) |
            Q(data__icontains=search_query)
        )

    status_filter = request.GET.get('status', '')
    if status_filter:
        data_entries = data_entries.filter(status=status_filter)

    # تصفية حسب التاريخ
    date_from = request.GET.get('date_from', '')
    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            data_entries = data_entries.filter(created_at__gte=date_from_obj)
        except ValueError:
            pass

    date_to = request.GET.get('date_to', '')
    if date_to:
        try:
            from datetime import datetime, timedelta
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            # إضافة يوم كامل للتاريخ النهائي
            date_to_obj_end = date_to_obj + timedelta(days=1)
            data_entries = data_entries.filter(created_at__lt=date_to_obj_end)
        except ValueError:
            pass

    # الحصول على الحقول المراد تصديرها
    fields = Field.objects.filter(department=department, show_in_export=True).order_by('group__order', 'order')

    # تحديد نوع التصدير
    export_type = request.GET.get('export_type', 'csv')

    if export_type == 'csv':
        # تصدير بتنسيق CSV
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{department.name}_data.csv"'

        writer = csv.writer(response)

        # كتابة رأس الجدول
        header = ['العنوان', 'الرقم المرجعي', 'الحالة', 'تاريخ الإنشاء']
        for field in fields:
            header.append(field.display_name)
        writer.writerow(header)

        # كتابة البيانات
        for entry in data_entries:
            row = [
                entry.title,
                entry.reference_number or '',
                entry.status,
                entry.created_at.strftime('%Y-%m-%d %H:%M')
            ]

            for field in fields:
                value = entry.get_data_value(field.name) if entry.data else ''
                row.append(value or '')

            writer.writerow(row)

        return response

    elif export_type == 'json':
        # تصدير بتنسيق JSON
        data = []

        for entry in data_entries:
            entry_data = {
                'title': entry.title,
                'reference_number': entry.reference_number,
                'status': entry.status,
                'created_at': entry.created_at.strftime('%Y-%m-%d %H:%M'),
                'data': {}
            }

            if entry.data:
                for field in fields:
                    entry_data['data'][field.display_name] = entry.get_data_value(field.name) or ''

            data.append(entry_data)

        response = HttpResponse(json.dumps(data, ensure_ascii=False, indent=4), content_type='application/json')
        response['Content-Disposition'] = f'attachment; filename="{department.name}_data.json"'
        return response

    elif export_type == 'excel':
        # تصدير بتنسيق Excel
        try:
            # استخدام الوظيفة المحسنة لتصدير البيانات إلى Excel
            from .utils import export_table_to_excel

            wb = export_table_to_excel(department.name, fields, data_entries)

            # إنشاء استجابة HTTP لتنزيل الملف
            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="{department.name}_data.xlsx"'

            # حفظ المصنف في الاستجابة
            wb.save(response)

            return response
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في تصدير البيانات إلى Excel: {str(e)}")
            print(f"تفاصيل الخطأ: {error_details}")
            messages.error(request, f'حدث خطأ أثناء تصدير البيانات إلى Excel: {str(e)}')
            return redirect('departments:data_list', pk=department.pk)

    else:
        messages.error(request, 'نوع التصدير غير مدعوم.')
        return redirect('departments:data_list', pk=department.pk)

@login_required
def department_data_report(request, pk):
    """عرض تقارير بيانات القسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    # الحصول على البيانات
    data_entries = DepartmentData.objects.filter(department=department)

    # الحصول على معلمات التقرير
    report_type = request.GET.get('report_type', 'summary')
    date_range = request.GET.get('date_range', 'all')
    group_by = request.GET.get('group_by', 'none')
    chart_type = request.GET.get('chart_type', 'bar')
    chart_field = request.GET.get('chart_field', 'status')

    # تصفية حسب التاريخ
    from datetime import datetime, timedelta
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    if date_range == 'today':
        today = datetime.now().date()
        data_entries = data_entries.filter(created_at__date=today)
    elif date_range == 'week':
        today = datetime.now().date()
        start_of_week = today - timedelta(days=today.weekday())
        data_entries = data_entries.filter(created_at__date__gte=start_of_week)
    elif date_range == 'month':
        today = datetime.now().date()
        start_of_month = today.replace(day=1)
        data_entries = data_entries.filter(created_at__date__gte=start_of_month)
    elif date_range == 'year':
        today = datetime.now().date()
        start_of_year = today.replace(month=1, day=1)
        data_entries = data_entries.filter(created_at__date__gte=start_of_year)
    elif date_range == 'custom':
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                data_entries = data_entries.filter(created_at__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
                # إضافة يوم كامل للتاريخ النهائي
                date_to_obj_end = date_to_obj + timedelta(days=1)
                data_entries = data_entries.filter(created_at__lt=date_to_obj_end)
            except ValueError:
                pass

    # الحصول على الحقول
    fields = Field.objects.filter(department=department).order_by('group__order', 'order')

    # الحقول المحددة للتقرير التفصيلي
    selected_fields = request.GET.get('selected_fields', '').split(',') if request.GET.get('selected_fields') else []
    selected_field_objects = []

    if selected_fields:
        selected_field_objects = Field.objects.filter(id__in=selected_fields).order_by('group__order', 'order')

    # إعداد بيانات التقرير
    context = {
        'department': department,
        'report_type': report_type,
        'date_range': date_range,
        'group_by': group_by,
        'chart_type': chart_type,
        'chart_field': chart_field,
        'date_from': date_from,
        'date_to': date_to,
        'fields': fields,
        'selected_fields': selected_fields,
        'selected_field_objects': selected_field_objects,
        'entries': data_entries,
    }

    # إعداد بيانات التقرير الملخص
    if report_type == 'summary':
        # إحصائيات عامة
        context['total_entries'] = data_entries.count()
        context['active_entries'] = data_entries.filter(status='active').count()
        context['inactive_entries'] = data_entries.filter(status='inactive').count()
        context['pending_entries'] = data_entries.filter(status='pending').count()

        # تجميع البيانات حسب المعيار المحدد
        if group_by != 'none':
            grouped_data = []

            if group_by == 'status':
                context['group_by_label'] = 'الحالة'
                status_counts = {
                    'active': data_entries.filter(status='active').count(),
                    'inactive': data_entries.filter(status='inactive').count(),
                    'pending': data_entries.filter(status='pending').count(),
                }

                status_labels = {
                    'active': 'نشط',
                    'inactive': 'غير نشط',
                    'pending': 'قيد الانتظار',
                }

                for status, count in status_counts.items():
                    if count > 0:
                        percentage = round((count / context['total_entries']) * 100, 2) if context['total_entries'] > 0 else 0
                        grouped_data.append({
                            'label': status_labels.get(status, status),
                            'count': count,
                            'percentage': percentage
                        })

            elif group_by == 'date':
                context['group_by_label'] = 'التاريخ'
                from django.db.models import Count
                from django.db.models.functions import TruncDate

                date_counts = data_entries.annotate(date=TruncDate('created_at')).values('date').annotate(count=Count('id')).order_by('date')

                for item in date_counts:
                    percentage = round((item['count'] / context['total_entries']) * 100, 2) if context['total_entries'] > 0 else 0
                    grouped_data.append({
                        'label': item['date'].strftime('%Y-%m-%d'),
                        'count': item['count'],
                        'percentage': percentage
                    })

            elif group_by == 'month':
                context['group_by_label'] = 'الشهر'
                from django.db.models import Count
                from django.db.models.functions import TruncMonth

                month_counts = data_entries.annotate(month=TruncMonth('created_at')).values('month').annotate(count=Count('id')).order_by('month')

                for item in month_counts:
                    percentage = round((item['count'] / context['total_entries']) * 100, 2) if context['total_entries'] > 0 else 0
                    grouped_data.append({
                        'label': item['month'].strftime('%Y-%m'),
                        'count': item['count'],
                        'percentage': percentage
                    })

            elif group_by == 'user':
                context['group_by_label'] = 'المستخدم'
                from django.db.models import Count
                from django.contrib.auth import get_user_model
                User = get_user_model()

                user_counts = data_entries.values('created_by').annotate(count=Count('id')).order_by('-count')

                for item in user_counts:
                    if item['created_by']:
                        user = User.objects.filter(id=item['created_by']).first()
                        user_name = user.get_full_name() or user.username if user else 'غير معروف'
                    else:
                        user_name = 'غير معروف'

                    percentage = round((item['count'] / context['total_entries']) * 100, 2) if context['total_entries'] > 0 else 0
                    grouped_data.append({
                        'label': user_name,
                        'count': item['count'],
                        'percentage': percentage
                    })

            context['grouped_data'] = grouped_data

    # إعداد بيانات الرسم البياني
    elif report_type == 'chart':
        chart_data = []
        chart_labels = []
        chart_values = []
        chart_title = ''

        if chart_field == 'status':
            chart_title = 'توزيع البيانات حسب الحالة'
            status_counts = {
                'active': data_entries.filter(status='active').count(),
                'inactive': data_entries.filter(status='inactive').count(),
                'pending': data_entries.filter(status='pending').count(),
            }

            status_labels = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'pending': 'قيد الانتظار',
            }

            for status, count in status_counts.items():
                if count > 0:
                    chart_labels.append(status_labels.get(status, status))
                    chart_values.append(count)

        elif chart_field == 'date':
            chart_title = 'توزيع البيانات حسب التاريخ'
            from django.db.models import Count
            from django.db.models.functions import TruncDate

            date_counts = data_entries.annotate(date=TruncDate('created_at')).values('date').annotate(count=Count('id')).order_by('date')

            for item in date_counts:
                chart_labels.append(item['date'].strftime('%Y-%m-%d'))
                chart_values.append(item['count'])

        else:
            # رسم بياني حسب حقل مخصص
            try:
                field = Field.objects.get(id=chart_field)
                chart_title = f'توزيع البيانات حسب {field.display_name}'

                # الحصول على قيم الحقل المخصص
                field_values = {}

                for entry in data_entries:
                    value = entry.get_data_value(field.name) if entry.data else ''
                    if value:
                        if value in field_values:
                            field_values[value] += 1
                        else:
                            field_values[value] = 1

                for value, count in field_values.items():
                    chart_labels.append(value)
                    chart_values.append(count)

            except Field.DoesNotExist:
                pass

        context['chart_data'] = True
        context['chart_labels'] = json.dumps(chart_labels)
        context['chart_values'] = json.dumps(chart_values)
        context['chart_title'] = chart_title

    # تصدير التقرير
    export_format = request.GET.get('export', '')

    if export_format:
        if export_format == 'excel':
            try:
                import openpyxl
                from openpyxl.styles import Font, Alignment, PatternFill

                # إنشاء مصنف جديد وورقة عمل
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = f"تقرير {department.name}"

                # إضافة عنوان التقرير
                ws.merge_cells('A1:E1')
                title_cell = ws['A1']
                title_cell.value = f"تقرير {department.name} - {datetime.now().strftime('%Y-%m-%d')}"
                title_cell.font = Font(size=16, bold=True)
                title_cell.alignment = Alignment(horizontal='center')

                # إضافة معلومات التقرير
                ws['A3'] = 'نوع التقرير:'
                ws['B3'] = 'ملخص' if report_type == 'summary' else 'تفصيلي' if report_type == 'detailed' else 'رسم بياني'

                ws['A4'] = 'الفترة الزمنية:'
                ws['B4'] = date_range

                ws['A5'] = 'إجمالي البيانات:'
                ws['B5'] = context.get('total_entries', 0)

                # إضافة البيانات حسب نوع التقرير
                if report_type == 'summary':
                    # إضافة ملخص الحالات
                    ws['A7'] = 'الحالة'
                    ws['B7'] = 'العدد'
                    ws['C7'] = 'النسبة المئوية'

                    ws['A8'] = 'نشط'
                    ws['B8'] = context.get('active_entries', 0)
                    ws['C8'] = f"{round((context.get('active_entries', 0) / context.get('total_entries', 1)) * 100, 2)}%"

                    ws['A9'] = 'غير نشط'
                    ws['B9'] = context.get('inactive_entries', 0)
                    ws['C9'] = f"{round((context.get('inactive_entries', 0) / context.get('total_entries', 1)) * 100, 2)}%"

                    ws['A10'] = 'قيد الانتظار'
                    ws['B10'] = context.get('pending_entries', 0)
                    ws['C10'] = f"{round((context.get('pending_entries', 0) / context.get('total_entries', 1)) * 100, 2)}%"

                    # إضافة البيانات المجمعة إذا وجدت
                    if group_by != 'none' and 'grouped_data' in context:
                        ws['A12'] = context.get('group_by_label', 'المجموعة')
                        ws['B12'] = 'العدد'
                        ws['C12'] = 'النسبة المئوية'

                        for i, group in enumerate(context['grouped_data']):
                            ws[f'A{13+i}'] = group['label']
                            ws[f'B{13+i}'] = group['count']
                            ws[f'C{13+i}'] = f"{group['percentage']}%"

                elif report_type == 'detailed':
                    # إضافة رأس الجدول
                    ws['A7'] = '#'
                    ws['B7'] = 'العنوان'
                    ws['C7'] = 'الرقم المرجعي'
                    ws['D7'] = 'الحالة'
                    ws['E7'] = 'تاريخ الإنشاء'

                    # إضافة أسماء الحقول المخصصة
                    col_index = 6  # F
                    for field in selected_field_objects:
                        ws.cell(row=7, column=col_index).value = field.display_name
                        col_index += 1

                    # إضافة البيانات
                    for i, entry in enumerate(data_entries):
                        row_index = 8 + i
                        ws.cell(row=row_index, column=1).value = i + 1
                        ws.cell(row=row_index, column=2).value = entry.title
                        ws.cell(row=row_index, column=3).value = entry.reference_number or ''
                        ws.cell(row=row_index, column=4).value = entry.status
                        ws.cell(row=row_index, column=5).value = entry.created_at.strftime('%Y-%m-%d %H:%M')

                        # إضافة قيم الحقول المخصصة
                        col_index = 6  # F
                        for field in selected_field_objects:
                            value = entry.get_data_value(field.name) if entry.data else ''
                            ws.cell(row=row_index, column=col_index).value = value or ''
                            col_index += 1

                # تنسيق الجدول
                for col in range(1, ws.max_column + 1):
                    ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 20

                # إنشاء استجابة HTTP لتنزيل الملف
                response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename="{department.name}_report.xlsx"'

                # حفظ المصنف في الاستجابة
                wb.save(response)

                return response

            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                print(f"خطأ في تصدير التقرير إلى Excel: {str(e)}")
                print(f"تفاصيل الخطأ: {error_details}")
                messages.error(request, f'حدث خطأ أثناء تصدير التقرير إلى Excel: {str(e)}')

        elif export_format == 'pdf':
            # يمكن إضافة تصدير PDF هنا في المستقبل
            messages.warning(request, 'تصدير PDF غير مدعوم حاليًا.')

    return render(request, 'departments/department_data_report.html', context)

@login_required
def department_data_import(request, pk):
    """استيراد بيانات للقسم"""
    department = get_object_or_404(Department, pk=pk)

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذا القسم.')
        return redirect('dashboard')

    if request.method == 'POST':
        # التحقق من وجود ملف
        if 'import_file' not in request.FILES:
            messages.error(request, 'يرجى تحديد ملف للاستيراد.')
            return redirect('departments:data_import', pk=department.pk)

        import_file = request.FILES['import_file']
        file_extension = import_file.name.split('.')[-1].lower()

        # الحصول على الحقول
        fields = Field.objects.filter(department=department)
        field_map = {field.display_name: field.name for field in fields}

        # استيراد البيانات حسب نوع الملف
        if file_extension == 'csv':
            # استيراد من CSV
            try:
                csv_data = csv.reader(io.StringIO(import_file.read().decode('utf-8')))
                rows = list(csv_data)

                if len(rows) < 2:
                    messages.error(request, 'الملف لا يحتوي على بيانات كافية.')
                    return redirect('departments:data_import', pk=department.pk)

                # استخراج رأس الجدول
                header = rows[0]

                # التحقق من وجود العنوان على الأقل
                if 'العنوان' not in header:
                    messages.error(request, 'الملف لا يحتوي على عمود "العنوان" المطلوب.')
                    return redirect('departments:data_import', pk=department.pk)

                # استيراد البيانات
                imported_count = 0
                for row in rows[1:]:
                    if len(row) < len(header):
                        continue  # تخطي الصفوف غير المكتملة

                    # إنشاء قاموس البيانات
                    row_data = dict(zip(header, row))

                    # إنشاء كائن البيانات
                    data_entry = DepartmentData(
                        department=department,
                        title=row_data.get('العنوان', ''),
                        reference_number=row_data.get('الرقم المرجعي', ''),
                        status=row_data.get('الحالة', 'active'),
                        created_by=request.user,
                        updated_by=request.user,
                        data={}
                    )

                    # إضافة البيانات المخصصة
                    for display_name, field_name in field_map.items():
                        if display_name in row_data:
                            data_entry.data[field_name] = row_data[display_name]

                    data_entry.save()

                    # إنشاء سجل تاريخ
                    DepartmentDataHistory.objects.create(
                        data_entry=data_entry,
                        data_snapshot=data_entry.data,
                        action='import',
                        user=request.user
                    )

                    imported_count += 1

                messages.success(request, f'تم استيراد {imported_count} سجل بنجاح.')
                return redirect('departments:data_list', pk=department.pk)

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء استيراد البيانات: {str(e)}')
                return redirect('departments:data_import', pk=department.pk)

        elif file_extension == 'json':
            # استيراد من JSON
            try:
                json_data = json.loads(import_file.read().decode('utf-8'))

                if not isinstance(json_data, list):
                    messages.error(request, 'الملف يجب أن يحتوي على مصفوفة من السجلات.')
                    return redirect('departments:data_import', pk=department.pk)

                # استيراد البيانات
                imported_count = 0
                for item in json_data:
                    if not isinstance(item, dict) or 'title' not in item:
                        continue  # تخطي العناصر غير الصالحة

                    # إنشاء كائن البيانات
                    data_entry = DepartmentData(
                        department=department,
                        title=item.get('title', ''),
                        reference_number=item.get('reference_number', ''),
                        status=item.get('status', 'active'),
                        created_by=request.user,
                        updated_by=request.user,
                        data={}
                    )

                    # إضافة البيانات المخصصة
                    if 'data' in item and isinstance(item['data'], dict):
                        for display_name, value in item['data'].items():
                            if display_name in field_map:
                                data_entry.data[field_map[display_name]] = value

                    data_entry.save()

                    # إنشاء سجل تاريخ
                    DepartmentDataHistory.objects.create(
                        data_entry=data_entry,
                        data_snapshot=data_entry.data,
                        action='import',
                        user=request.user
                    )

                    imported_count += 1

                messages.success(request, f'تم استيراد {imported_count} سجل بنجاح.')
                return redirect('departments:data_list', pk=department.pk)

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء استيراد البيانات: {str(e)}')
                return redirect('departments:data_import', pk=department.pk)

        else:
            messages.error(request, 'نوع الملف غير مدعوم. يرجى استخدام ملفات CSV أو JSON.')
            return redirect('departments:data_import', pk=department.pk)

    context = {
        'department': department,
    }

    return render(request, 'departments/department_data_import.html', context)

@login_required
def department_designer(request, pk):
    """واجهة تصميم القسم بالسحب والإفلات"""
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    department = get_object_or_404(Department, pk=pk)

    # الحصول على مجموعات الحقول والحقول
    field_groups = FieldGroup.objects.filter(department=department).order_by('order')
    ungrouped_fields = Field.objects.filter(department=department, group__isnull=True).order_by('order')

    context = {
        'department': department,
        'field_groups': field_groups,
        'ungrouped_fields': ungrouped_fields,
    }

    return render(request, 'departments/department_designer.html', context)

@login_required
@require_POST
def reorder_fields(request, pk):
    """إعادة ترتيب الحقول ومجموعات الحقول"""
    if request.user.user_type != 'admin':
        return JsonResponse({'status': 'error', 'message': 'ليس لديك صلاحية الوصول إلى هذه الوظيفة.'})

    department = get_object_or_404(Department, pk=pk)

    try:
        data = json.loads(request.body)

        # إعادة ترتيب مجموعات الحقول
        if 'groups' in data:
            with transaction.atomic():
                for i, group_id in enumerate(data['groups']):
                    FieldGroup.objects.filter(id=group_id, department=department).update(order=i)

        # إعادة ترتيب الحقول
        if 'fields' in data:
            with transaction.atomic():
                for field_data in data['fields']:
                    field_id = field_data['id']
                    group_id = field_data.get('group_id')
                    order = field_data['order']

                    if group_id:
                        Field.objects.filter(id=field_id, department=department).update(
                            group_id=group_id,
                            order=order
                        )
                    else:
                        Field.objects.filter(id=field_id, department=department).update(
                            group=None,
                            order=order
                        )

        return JsonResponse({'status': 'success'})

    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})

@login_required
def field_options(request, pk):
    """الحصول على خيارات الحقل"""
    field = get_object_or_404(Field, pk=pk)
    department = field.department

    # التحقق من صلاحية الوصول
    if not department.can_user_access(request.user):
        return JsonResponse({'status': 'error', 'message': 'ليس لديك صلاحية الوصول إلى هذا الحقل.'})

    options = field.get_options_dict()

    return JsonResponse({
        'status': 'success',
        'field_id': field.id,
        'field_name': field.name,
        'field_type': field.type,
        'options': options
    })
