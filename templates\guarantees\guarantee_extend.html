{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تمديد كفالة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تمديد كفالة</h1>
    <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى تفاصيل الكفالة
    </a>
</div>

<div class="card">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">تمديد الكفالة للمستفيد "{{ guarantee.beneficiary_name }}"</h5>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <p><strong>تاريخ الانتهاء الحالي:</strong> {{ guarantee.end_date }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>المدة الحالية:</strong> {{ guarantee.duration }} يوم</p>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.days.id_for_label }}">{{ form.days.label }}</label>
                {{ form.days }}
                {% if form.days.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.days.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
                <small class="form-text text-muted">أدخل عدد الأيام التي ترغب في تمديد الكفالة بها.</small>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-info">
                    <i class="fas fa-calendar-plus"></i> تمديد الكفالة
                </button>
                <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
