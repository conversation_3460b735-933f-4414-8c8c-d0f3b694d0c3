{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}الإشعارات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>الإشعارات</h1>
    <a href="{% url 'dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى لوحة التحكم
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">قائمة الإشعارات</h5>
    </div>
    <div class="card-body">
        <div class="list-group">
            {% for notification in notifications %}
            <div class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-warning{% endif %}">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1">{{ notification.title }}</h5>
                    <small>{{ notification.created_at|date:"Y-m-d H:i" }}</small>
                </div>
                <p class="mb-1">{{ notification.message }}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <small>
                        {% if notification.type == 'info' %}
                        <span class="badge badge-info">معلومات</span>
                        {% elif notification.type == 'warning' %}
                        <span class="badge badge-warning">تحذير</span>
                        {% elif notification.type == 'danger' %}
                        <span class="badge badge-danger">خطر</span>
                        {% else %}
                        <span class="badge badge-secondary">إشعار</span>
                        {% endif %}
                    </small>
                    {% if not notification.is_read %}
                    <a href="{% url 'core:mark_notification_as_read' notification.id %}" class="btn btn-sm btn-success">
                        <i class="fas fa-check"></i> تعليم كمقروء
                    </a>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <p class="text-center">لا توجد إشعارات.</p>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
