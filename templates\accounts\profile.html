{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>الملف الشخصي</h1>
    <a href="{% url 'dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى لوحة التحكم
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">تعديل الملف الشخصي</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.first_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.last_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <a href="{% url 'accounts:change_password' %}" class="btn btn-warning">
                            <i class="fas fa-key"></i> تغيير كلمة المرور
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">معلومات الحساب</h5>
            </div>
            <div class="card-body">
                <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                <p>
                    <strong>نوع الحساب:</strong>
                    {% if user.user_type == 'admin' %}
                    <span class="badge badge-danger">مدير النظام</span>
                    {% elif user.user_type == 'department_manager' %}
                    <span class="badge badge-warning">مدير قسم</span>
                    {% else %}
                    <span class="badge badge-info">مدخل بيانات</span>
                    {% endif %}
                </p>
                <p><strong>تاريخ الانضمام:</strong> {{ user.date_joined|date:"Y-m-d" }}</p>
                <p><strong>آخر تسجيل دخول:</strong> {{ user.last_login|date:"Y-m-d H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
