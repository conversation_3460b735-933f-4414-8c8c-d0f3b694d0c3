# Generated by Django 5.2 on 2025-04-19 00:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Committee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان اللجنة')),
                ('order_number', models.CharField(max_length=100, verbose_name='رقم الأمر الإداري')),
                ('order_date', models.DateField(verbose_name='تاريخ التشكيل')),
                ('type', models.CharField(choices=[('investigation', 'لجنة تحقيقية'), ('council', 'مجلس تحقيقي'), ('joint', 'لجنة مشتركة')], max_length=20, verbose_name='نوع اللجنة')),
                ('duration', models.PositiveIntegerField(verbose_name='مدة اللجنة (بالأيام)')),
                ('end_date', models.DateField(verbose_name='تاريخ انتهاء اللجنة')),
                ('status', models.CharField(choices=[('active', 'نشطة'), ('extended', 'ممددة'), ('closed', 'مغلقة')], default='active', max_length=20, verbose_name='حالة اللجنة')),
                ('last_action', models.TextField(blank=True, null=True, verbose_name='آخر الإجراءات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='committees', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'لجنة',
                'verbose_name_plural': 'اللجان',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommitteeMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العضو')),
                ('role', models.CharField(choices=[('chairman', 'رئيس'), ('member', 'عضو'), ('secretary', 'سكرتير')], max_length=20, verbose_name='دور العضو')),
                ('committee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='members', to='committees.committee', verbose_name='اللجنة')),
            ],
            options={
                'verbose_name': 'عضو لجنة',
                'verbose_name_plural': 'أعضاء اللجنة',
                'ordering': ['role', 'name'],
            },
        ),
    ]
