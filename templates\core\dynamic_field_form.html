{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}
{% if field %}تعديل حقل مخصص{% else %}إضافة حقل مخصص{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        {% if field %}تعديل حقل مخصص: {{ field.display_name }}{% else %}إضافة حقل مخصص جديد{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.section.id_for_label }}" class="form-label">{{ form.section.label }}</label>
                                {{ form.section }}
                                {% if form.section.errors %}
                                <div class="text-danger">{{ form.section.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.field_type.id_for_label }}" class="form-label">{{ form.field_type.label }}</label>
                                {{ form.field_type }}
                                {% if form.field_type.errors %}
                                <div class="text-danger">{{ form.field_type.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.display_name.id_for_label }}" class="form-label">{{ form.display_name.label }}</label>
                                {{ form.display_name }}
                                {% if form.display_name.errors %}
                                <div class="text-danger">{{ form.display_name.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">يجب أن يكون الاسم البرمجي بدون مسافات أو أحرف خاصة.</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.order.id_for_label }}" class="form-label">{{ form.order.label }}</label>
                                {{ form.order }}
                                {% if form.order.errors %}
                                <div class="text-danger">{{ form.order.errors }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    {{ form.required }}
                                    <label class="form-check-label" for="{{ form.required.id_for_label }}">
                                        {{ form.required.label }}
                                    </label>
                                </div>
                                {% if form.required.errors %}
                                <div class="text-danger">{{ form.required.errors }}</div>
                                {% endif %}
                                
                                <div class="form-check mt-2">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                <div class="text-danger">{{ form.is_active.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row" id="options-row" style="display: none;">
                            <div class="col-12 mb-3">
                                <label for="{{ form.options.id_for_label }}" class="form-label">{{ form.options.label }}</label>
                                {{ form.options }}
                                {% if form.options.errors %}
                                <div class="text-danger">{{ form.options.errors }}</div>
                                {% endif %}
                                <small class="form-text text-muted">للقوائم المنسدلة فقط. أدخل الخيارات بتنسيق JSON، مثال: {"1": "الخيار الأول", "2": "الخيار الثاني"}</small>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">حفظ</button>
                            <a href="{% url 'core:dynamic_fields' %}" class="btn btn-secondary">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // إظهار/إخفاء حقل الخيارات بناءً على نوع الحقل
        function toggleOptionsField() {
            var fieldType = $('#{{ form.field_type.id_for_label }}').val();
            if (fieldType === 'select') {
                $('#options-row').show();
            } else {
                $('#options-row').hide();
            }
        }
        
        // تنفيذ الدالة عند تحميل الصفحة
        toggleOptionsField();
        
        // تنفيذ الدالة عند تغيير نوع الحقل
        $('#{{ form.field_type.id_for_label }}').change(function() {
            toggleOptionsField();
        });
        
        {% if field and field.is_default %}
        // تعطيل حقول معينة للحقول الافتراضية
        $('#{{ form.name.id_for_label }}').prop('disabled', true);
        $('#{{ form.field_type.id_for_label }}').prop('disabled', true);
        $('#{{ form.section.id_for_label }}').prop('disabled', true);
        {% endif %}
    });
</script>
{% endblock %}
