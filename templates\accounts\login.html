{% extends 'base_auth.html' %}
{% load i18n %}
{% load static %}

{% block title %}تسجيل الدخول{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header bg-primary text-white text-center">
        <h4>تسجيل الدخول</h4>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            {% if messages %}
            <div class="messages">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" name="username" id="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" name="password" id="password" class="form-control" required>
            </div>
            <div class="form-group form-check">
                <input type="checkbox" name="remember" id="remember" class="form-check-input">
                <label for="remember" class="form-check-label">تذكرني</label>
            </div>
            <button type="submit" class="btn btn-primary btn-block">تسجيل الدخول</button>
        </form>
    </div>
</div>
{% endblock %}
