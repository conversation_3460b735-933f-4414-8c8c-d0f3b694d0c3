{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إدارة المستخدمين</h1>
    <a href="{% url 'accounts:user_create' %}" class="btn btn-primary">
        <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">قائمة المستخدمين</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>نوع الحساب</th>
                        <th>آخر تسجيل دخول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_obj in users %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ user_obj.username }}</td>
                        <td>{{ user_obj.get_full_name }}</td>
                        <td>{{ user_obj.email }}</td>
                        <td>
                            {% if user_obj.user_type == 'admin' %}
                            <span class="badge badge-danger">مدير النظام</span>
                            {% elif user_obj.user_type == 'department_manager' %}
                            <span class="badge badge-warning">مدير قسم</span>
                            {% else %}
                            <span class="badge badge-info">مدخل بيانات</span>
                            {% endif %}
                        </td>
                        <td>{{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل الدخول بعد" }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'accounts:user_detail' user_obj.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'accounts:user_update' user_obj.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user_obj.id != user.id %}
                                <a href="{% url 'accounts:user_delete' user_obj.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">لا يوجد مستخدمين مسجلين.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
