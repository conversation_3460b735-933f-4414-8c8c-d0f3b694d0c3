{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تفاصيل القسم - {{ department.name }}{% endblock %}

{% block extra_css %}
<style>
    .department-icon {
        font-size: 2rem;
        margin-bottom: 15px;
        color: {{ department.color }};
    }
    .badge-custom {
        background-color: {{ department.color }};
        color: white;
    }
    .field-group {
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .field-group-header {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
    }
    .field-group-body {
        padding: 15px;
    }
    .action-buttons {
        margin-bottom: 20px;
    }
    .stats-card {
        text-align: center;
        margin-bottom: 20px;
    }
    .stats-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
    }
    .stats-label {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        {% if department.icon %}<i class="fas {{ department.icon }}" style="color: {{ department.color }};"></i>{% endif %}
        {{ department.name }}
    </h1>
    <div>
        <a href="{% url 'departments:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة الأقسام
        </a>
        <a href="{% url 'departments:update' department.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'departments:delete' department.id %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="action-buttons">
    <a href="{% url 'departments:data_list' department.id %}" class="btn btn-primary">
        <i class="fas fa-database"></i> عرض بيانات الإدخال
    </a>
    <a href="{% url 'departments:data_create' department.id %}" class="btn btn-success">
        <i class="fas fa-plus"></i> إضافة بيانات إدخال جديدة
    </a>
    <a href="{% url 'departments:designer' department.id %}" class="btn btn-info">
        <i class="fas fa-drafting-compass"></i> مصمم القسم
    </a>
    <a href="{% url 'departments:data_export' department.id %}" class="btn btn-secondary">
        <i class="fas fa-file-export"></i> تصدير البيانات
    </a>
    <a href="{% url 'departments:data_import' department.id %}" class="btn btn-warning">
        <i class="fas fa-file-import"></i> استيراد البيانات
    </a>
</div>

<div class="row">
    <!-- إحصائيات -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">إحصائيات القسم</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon text-primary">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="stats-number">{{ department.data_entries.count }}</div>
                            <div class="stats-label">إجمالي البيانات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon text-success">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <div class="stats-number">{{ department.fields.count }}</div>
                            <div class="stats-label">عدد الحقول</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon text-info">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="stats-number">{{ department.field_groups.count }}</div>
                            <div class="stats-label">عدد المجموعات</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon text-warning">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="stats-number">{{ department.updated_at|date:"Y-m-d" }}</div>
                            <div class="stats-label">آخر تحديث</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات القسم -->
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات القسم</h5>
            </div>
            <div class="card-body">
                <div class="department-icon text-center">
                    <i class="fas {{ department.icon }}"></i>
                </div>

                <p><strong>الاسم:</strong> {{ department.name }}</p>
                <p><strong>اسم الجدول:</strong> {{ department.table_name }}</p>
                <p><strong>الترتيب:</strong> {{ department.order }}</p>
                <p>
                    <strong>الحالة:</strong>
                    {% if department.is_active %}
                    <span class="badge badge-success">نشط</span>
                    {% else %}
                    <span class="badge badge-danger">غير نشط</span>
                    {% endif %}
                </p>
                <p>
                    <strong>الظهور في القائمة:</strong>
                    {% if department.show_in_menu %}
                    <span class="badge badge-success">نعم</span>
                    {% else %}
                    <span class="badge badge-danger">لا</span>
                    {% endif %}
                </p>
                <p>
                    <strong>مستوى الوصول:</strong>
                    <span class="badge badge-custom">{{ department.get_access_level_display }}</span>
                </p>
                <p><strong>مدير القسم:</strong> {{ department.manager.get_full_name|default:department.manager.username|default:"غير محدد" }}</p>
                <p><strong>الوصف:</strong></p>
                <p>{{ department.description|linebreaks|default:"لا يوجد وصف." }}</p>
            </div>
        </div>

        <!-- المستخدمون المصرح لهم -->
        {% if department.access_level == 'specific' and department.authorized_users.exists %}
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">المستخدمون المصرح لهم</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    {% for user in department.authorized_users.all %}
                    <li class="list-group-item">
                        <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-8">
        <!-- مجموعات الحقول والحقول -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">هيكل البيانات</h5>
                <div>
                    <a href="{% url 'departments:add_field_group' department.id %}" class="btn btn-sm btn-light mr-2">
                        <i class="fas fa-layer-group"></i> إضافة مجموعة
                    </a>
                    <a href="{% url 'departments:add_field' department.id %}" class="btn btn-sm btn-light">
                        <i class="fas fa-plus"></i> إضافة حقل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- مجموعات الحقول -->
                {% for group in department.field_groups.all %}
                <div class="field-group mb-4">
                    <div class="field-group-header d-flex justify-content-between align-items-center">
                        <span>{{ group.name }}</span>
                        <div>
                            <a href="{% url 'departments:add_field_to_group' department.id group.id %}" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> إضافة حقل
                            </a>
                            <a href="{% url 'departments:update_field_group' group.id %}" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'departments:delete_field_group' group.id %}" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                    <div class="field-group-body">
                        {% if group.fields.exists %}
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>اسم العرض</th>
                                        <th>النوع</th>
                                        <th>مطلوب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for field in group.fields.all %}
                                    <tr>
                                        <td>{{ field.name }}</td>
                                        <td>{{ field.display_name }}</td>
                                        <td><span class="badge badge-info">{{ field.get_type_display }}</span></td>
                                        <td>
                                            {% if field.required %}
                                            <span class="badge badge-danger">نعم</span>
                                            {% else %}
                                            <span class="badge badge-secondary">لا</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'departments:update_field' field.id %}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'departments:delete_field' field.id %}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> لا توجد حقول في هذه المجموعة.
                            <a href="{% url 'departments:add_field_to_group' department.id group.id %}" class="alert-link">إضافة حقل جديد</a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}

                <!-- الحقول غير المجمعة -->
                <div class="field-group">
                    <div class="field-group-header">
                        حقول غير مجمعة
                    </div>
                    <div class="field-group-body">
                        {% with ungrouped_fields=department.fields.filter|dictsort:"order" %}
                        {% if ungrouped_fields %}
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>اسم العرض</th>
                                        <th>النوع</th>
                                        <th>مطلوب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for field in ungrouped_fields %}
                                    {% if not field.group %}
                                    <tr>
                                        <td>{{ field.name }}</td>
                                        <td>{{ field.display_name }}</td>
                                        <td><span class="badge badge-info">{{ field.get_type_display }}</span></td>
                                        <td>
                                            {% if field.required %}
                                            <span class="badge badge-danger">نعم</span>
                                            {% else %}
                                            <span class="badge badge-secondary">لا</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'departments:update_field' field.id %}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'departments:delete_field' field.id %}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> لا توجد حقول غير مجمعة.
                            <a href="{% url 'departments:add_field' department.id %}" class="alert-link">إضافة حقل جديد</a>
                        </div>
                        {% endif %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
