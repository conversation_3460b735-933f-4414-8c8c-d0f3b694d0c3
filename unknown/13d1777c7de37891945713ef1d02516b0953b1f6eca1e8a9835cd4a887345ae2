{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}
{% if user_obj %}تعديل مستخدم{% else %}إضافة مستخدم جديد{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if user_obj %}تعديل مستخدم{% else %}إضافة مستخدم جديد{% endif %}</h1>
    <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى قائمة المستخدمين
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if user_obj %}تعديل مستخدم{% else %}إضافة مستخدم جديد{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.email.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.first_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.last_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.phone.id_for_label }}">{{ form.phone.label }}</label>
                        {{ form.phone }}
                        {% if form.phone.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.phone.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.user_type.id_for_label }}">{{ form.user_type.label }}</label>
                        {{ form.user_type }}
                        {% if form.user_type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.user_type.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            {% if not user_obj %}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password1.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password2.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            
            <div class="form-group">
                <div class="form-check">
                    {{ form.is_active }}
                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                        {{ form.is_active.label }}
                    </label>
                </div>
                {% if form.is_active.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.is_active.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
