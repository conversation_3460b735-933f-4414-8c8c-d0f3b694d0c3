{% extends 'base.html' %}

{% block title %}حذف مجموعة الحقول - {{ group.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>حذف مجموعة الحقول</h1>
    <div>
        <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل القسم
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد الحذف</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تحذير:</strong> هل أنت متأكد من رغبتك في حذف مجموعة الحقول "{{ group.name }}"؟
        </div>
        
        {% if group.fields.count > 0 %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <strong>تنبيه:</strong> تحتوي هذه المجموعة على {{ group.fields.count }} حقل. عند الحذف، سيتم نقل هذه الحقول إلى قائمة الحقول غير المجمعة.
        </div>
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            <div class="mt-3">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
