{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تفاصيل الكفالة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تفاصيل الكفالة</h1>
    <div>
        <a href="{% url 'guarantees:list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة الكفالات
        </a>
        <a href="{% url 'guarantees:update' guarantee.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        {% if not guarantee.is_expired %}
        <a href="{% url 'guarantees:extend' guarantee.id %}" class="btn btn-info">
            <i class="fas fa-calendar-plus"></i> تمديد
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات الكفالة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم المستفيد:</strong> {{ guarantee.beneficiary_name }}</p>
                        <p><strong>المبلغ:</strong> {{ guarantee.amount }}</p>
                        <p><strong>المصرف:</strong> {{ guarantee.bank_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المدة:</strong> {{ guarantee.duration }} يوم</p>
                        <p><strong>تاريخ البدء:</strong> {{ guarantee.start_date }}</p>
                        <p>
                            <strong>تاريخ الانتهاء:</strong> {{ guarantee.end_date }}
                            {% if guarantee.is_expired %}
                            <span class="badge badge-danger">منتهية</span>
                            {% elif guarantee.is_expiring_soon %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% else %}
                            <span class="badge badge-success">نشطة</span>
                            {% endif %}
                        </p>
                        <p><strong>تم الإنشاء بواسطة:</strong> {{ guarantee.created_by.get_full_name|default:guarantee.created_by.username }}</p>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h6>ملاحظات:</h6>
                        <p>{{ guarantee.notes|linebreaks|default:"لا توجد ملاحظات." }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المستندات</h5>
                <a href="{% url 'guarantees:upload_document' guarantee.id %}" class="btn btn-sm btn-light">
                    <i class="fas fa-upload"></i> رفع مستند
                </a>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for file in files %}
                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt"></i>
                            <a href="{{ file.file.url }}" target="_blank">{{ file.name }}</a>
                            <small class="d-block text-muted">{{ file.uploaded_at|date:"Y-m-d H:i" }}</small>
                        </div>
                        <div class="btn-group">
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-primary" download>
                                <i class="fas fa-download" title="تحميل"></i>
                            </a>
                            <a href="{{ file.file.url }}" class="btn btn-sm btn-info" target="_blank">
                                <i class="fas fa-eye" title="عرض"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-secondary print-file" data-url="{{ file.file.url }}">
                                <i class="fas fa-print" title="طباعة"></i>
                            </button>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-center">لا توجد مستندات مرفقة.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">معلومات النظام</h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong> {{ guarantee.created_at|date:"Y-m-d H:i" }}</p>
                <p><strong>تاريخ آخر تحديث:</strong> {{ guarantee.updated_at|date:"Y-m-d H:i" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // معالجة زر الطباعة
        $('.print-file').on('click', function() {
            const fileUrl = $(this).data('url');

            // إنشاء إطار مخفي للطباعة
            const printFrame = $('<iframe>', {
                name: 'printFrame',
                class: 'print-frame',
                style: 'position:absolute;width:0;height:0;left:-1000px;top:-1000px;'
            }).appendTo('body');

            // تحميل الملف في الإطار ثم طباعته
            printFrame.on('load', function() {
                try {
                    // محاولة الطباعة
                    frames['printFrame'].focus();
                    frames['printFrame'].print();

                    // إزالة الإطار بعد الطباعة
                    setTimeout(function() {
                        printFrame.remove();
                    }, 1000);
                } catch (e) {
                    // في حالة حدوث خطأ، افتح الملف في نافذة جديدة للطباعة
                    alert('لا يمكن طباعة هذا الملف مباشرة. سيتم فتحه في نافذة جديدة للطباعة.');
                    window.open(fileUrl, '_blank');
                }
            });

            // تعيين مصدر الإطار
            printFrame.attr('src', fileUrl);
        });
    });
</script>
{% endblock %}