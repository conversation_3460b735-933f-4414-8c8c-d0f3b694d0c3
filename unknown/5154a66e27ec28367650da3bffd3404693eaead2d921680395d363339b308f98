{% extends 'base.html' %}

{% block title %}استيراد بيانات - {{ department.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>استيراد بيانات</h1>
    <div>
        <a href="{% url 'departments:data_list' department.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة البيانات
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">استيراد بيانات إلى {{ department.name }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> يمكنك استيراد البيانات من ملفات CSV أو JSON. تأكد من أن الملف يحتوي على العمود "العنوان" على الأقل.
        </div>
        
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="form-group">
                <label for="import_file">اختر ملف للاستيراد:</label>
                <input type="file" name="import_file" id="import_file" class="form-control-file" required>
                <small class="form-text text-muted">الصيغ المدعومة: CSV, JSON</small>
            </div>
            
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-import"></i> استيراد
                </button>
                <a href="{% url 'departments:data_list' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
        
        <hr>
        
        <h5>تنسيق الملفات</h5>
        
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">تنسيق CSV</h6>
            </div>
            <div class="card-body">
                <p>يجب أن يحتوي ملف CSV على الأعمدة التالية:</p>
                <ul>
                    <li><strong>العنوان</strong> (مطلوب): عنوان البيانات</li>
                    <li><strong>الرقم المرجعي</strong> (اختياري): الرقم المرجعي للبيانات</li>
                    <li><strong>الحالة</strong> (اختياري): حالة البيانات (active, inactive, pending)</li>
                    <li><strong>أسماء الحقول</strong>: يجب أن تتطابق مع أسماء العرض للحقول في القسم</li>
                </ul>
                <p>مثال:</p>
                <pre>العنوان,الرقم المرجعي,الحالة,الاسم,العمر,تاريخ الميلاد
"بيانات 1","REF001","active","أحمد محمد","30","1990-01-01"
"بيانات 2","REF002","pending","سارة أحمد","25","1995-05-15"</pre>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">تنسيق JSON</h6>
            </div>
            <div class="card-body">
                <p>يجب أن يكون ملف JSON عبارة عن مصفوفة من الكائنات، كل كائن يمثل سجل بيانات:</p>
                <pre>{
  "title": "بيانات 1",
  "reference_number": "REF001",
  "status": "active",
  "data": {
    "الاسم": "أحمد محمد",
    "العمر": "30",
    "تاريخ الميلاد": "1990-01-01"
  }
}</pre>
                <p>ملاحظة: يجب أن تتطابق أسماء الحقول في كائن "data" مع أسماء العرض للحقول في القسم.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
