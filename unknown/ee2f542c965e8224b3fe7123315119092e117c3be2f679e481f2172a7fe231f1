# Generated by Django 5.2 on 2025-04-19 00:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف القسم')),
                ('icon', models.CharField(blank=True, max_length=50, null=True, verbose_name='أيقونة القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Field',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحقل')),
                ('display_name', models.CharField(max_length=100, verbose_name='اسم العرض')),
                ('type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('date', 'تاريخ'), ('select', 'قائمة منسدلة'), ('textarea', 'نص طويل'), ('file', 'ملف')], max_length=20, verbose_name='نوع الحقل')),
                ('required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('options', models.JSONField(blank=True, null=True, verbose_name='خيارات')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields', to='departments.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'حقل',
                'verbose_name_plural': 'الحقول',
                'ordering': ['order', 'name'],
            },
        ),
    ]
