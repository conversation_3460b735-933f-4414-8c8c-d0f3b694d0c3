{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
{% if data_entry %}تعديل بيانات{% else %}إضافة بيانات جديدة{% endif %} - {{ department.name }}
{% endblock %}

{% block extra_css %}
<style>
    .field-group {
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .field-group-header {
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
        cursor: pointer;
    }
    .field-group-header:hover {
        background-color: #e9ecef;
    }
    .field-group-body {
        padding: 15px;
    }
    .field-group-header .toggle-icon {
        float: left;
    }
    .required-field label::after {
        content: " *";
        color: red;
    }
    .form-group {
        margin-bottom: 1rem;
    }
    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }
    .form-control {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    select.form-control {
        height: calc(2.25rem + 2px);
    }
    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 80%;
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if data_entry %}تعديل بيانات الإدخال{% else %}إضافة بيانات إدخال جديدة{% endif %}</h1>
    <div>
        <a href="{% if data_entry %}{% url 'departments:data_detail' data_entry.id %}{% else %}{% url 'departments:data_list' department.id %}{% endif %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
</div>

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}

    <!-- تم إزالة قسم معلومات أساسية بناءً على طلب المستخدم -->
    <input type="hidden" name="title" value="بيانات جديدة">
    <input type="hidden" name="reference_number" value="">
    <input type="hidden" name="status" value="active">

    <!-- الحقول الديناميكية -->
    <div class="card shadow mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">بيانات الإدخال</h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for field in form %}
                    {% if field.name|slice:":6" == "field_" %}
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ field.id_for_label }}">
                                    {{ field.label }}
                                    {% if field.field.required %}<span class="text-danger">*</span>{% endif %}
                                </label>
                                {{ field }}
                                {% if field.help_text %}
                                <small class="form-text text-muted">{{ field.help_text }}</small>
                                {% endif %}
                                {% if field.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in field.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="mt-3">
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> حفظ
        </button>
        <a href="{% if data_entry %}{% url 'departments:data_detail' data_entry.id %}{% else %}{% url 'departments:data_list' department.id %}{% endif %}" class="btn btn-secondary">
            <i class="fas fa-times"></i> إلغاء
        </a>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تبديل عرض/إخفاء المجموعات
        $('.field-group-header').click(function() {
            $(this).find('.toggle-icon i').toggleClass('fa-chevron-down fa-chevron-up');
        });

        // إضافة فئة form-control إلى جميع حقول النموذج
        $('input, select, textarea').each(function() {
            if (!$(this).hasClass('form-control') && $(this).attr('type') !== 'file' && $(this).attr('type') !== 'checkbox' && $(this).attr('type') !== 'radio') {
                $(this).addClass('form-control');
            }
        });

        // معالجة حقول التاريخ
        $('input[type="date"]').each(function() {
            $(this).attr('class', 'form-control');
        });

        // معالجة حقول الملفات
        $('input[type="file"]').each(function() {
            $(this).attr('class', 'form-control-file');
        });

        // معالجة حقول الاختيار
        $('input[type="checkbox"], input[type="radio"]').each(function() {
            $(this).addClass('form-check-input');
            $(this).parent().addClass('form-check');
        });
    });
</script>
{% endblock %}
