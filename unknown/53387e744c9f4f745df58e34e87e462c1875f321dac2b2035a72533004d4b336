{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تفاصيل المستخدم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تفاصيل المستخدم</h1>
    <div>
        <a href="{% url 'accounts:user_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى قائمة المستخدمين
        </a>
        <a href="{% url 'accounts:user_update' user_obj.id %}" class="btn btn-warning">
            <i class="fas fa-edit"></i> تعديل
        </a>
        {% if user_obj.id != user.id %}
        <a href="{% url 'accounts:user_delete' user_obj.id %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم المستخدم:</strong> {{ user_obj.username }}</p>
                        <p><strong>الاسم الكامل:</strong> {{ user_obj.get_full_name }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ user_obj.email }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>رقم الهاتف:</strong> {{ user_obj.phone|default:"غير محدد" }}</p>
                        <p>
                            <strong>نوع الحساب:</strong>
                            {% if user_obj.user_type == 'admin' %}
                            <span class="badge badge-danger">مدير النظام</span>
                            {% elif user_obj.user_type == 'department_manager' %}
                            <span class="badge badge-warning">مدير قسم</span>
                            {% else %}
                            <span class="badge badge-info">مدخل بيانات</span>
                            {% endif %}
                        </p>
                        <p>
                            <strong>الحالة:</strong>
                            {% if user_obj.is_active %}
                            <span class="badge badge-success">نشط</span>
                            {% else %}
                            <span class="badge badge-danger">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">معلومات النظام</h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الانضمام:</strong> {{ user_obj.date_joined|date:"Y-m-d H:i" }}</p>
                <p><strong>آخر تسجيل دخول:</strong> {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل الدخول بعد" }}</p>
                <p>
                    <strong>صلاحيات إضافية:</strong>
                    {% if user_obj.is_staff %}
                    <span class="badge badge-info">طاقم الإدارة</span>
                    {% endif %}
                    {% if user_obj.is_superuser %}
                    <span class="badge badge-danger">مدير عام</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
