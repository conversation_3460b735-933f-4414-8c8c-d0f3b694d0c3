from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.contenttypes.models import ContentType
from committees.models import Committee
from guarantees.models import Guarantee
from accounts.models import User
from .models import Notification, Setting, Backup, File, DynamicField, DynamicData
from .forms import DynamicFieldForm, DynamicDataForm, SettingsForm
from django.utils import timezone
from datetime import timedelta
from django.contrib import messages
import json

@login_required
def dashboard(request):
    # إحصائيات اللجان
    committees = Committee.objects.all()
    active_committees = committees.filter(status='active').count()
    closed_committees = committees.filter(status='closed').count()
    expiring_committees = committees.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7),
        end_date__gte=timezone.now().date()
    ).count()

    # إحصائيات الكفالات
    guarantees = Guarantee.objects.all()
    active_guarantees = guarantees.filter(end_date__gte=timezone.now().date()).count()
    expired_guarantees = guarantees.filter(end_date__lt=timezone.now().date()).count()
    expiring_guarantees = guarantees.filter(
        end_date__lte=timezone.now().date() + timedelta(days=7),
        end_date__gte=timezone.now().date()
    ).count()

    # إحصائيات المستخدمين
    users = User.objects.all()
    admins = users.filter(user_type='admin').count()
    department_managers = users.filter(user_type='department_manager').count()
    data_entries = users.filter(user_type='data_entry').count()

    # الإشعارات
    notifications = request.user.notifications.filter(is_read=False).order_by('-created_at')[:5]

    # بيانات الرسوم البيانية
    committees_by_month = get_committees_by_month()
    guarantees_by_month = get_guarantees_by_month()

    context = {
        'active_committees': active_committees,
        'closed_committees': closed_committees,
        'expiring_committees': expiring_committees,
        'active_guarantees': active_guarantees,
        'expired_guarantees': expired_guarantees,
        'expiring_guarantees': expiring_guarantees,
        'admins': admins,
        'department_managers': department_managers,
        'data_entries': data_entries,
        'notifications': notifications,
        'committees_by_month': committees_by_month,
        'guarantees_by_month': guarantees_by_month,
    }

    return render(request, 'core/dashboard.html', context)

def get_committees_by_month():
    data = []
    months = []

    for i in range(12):
        date = timezone.now() - timedelta(days=30 * i)
        month = date.strftime('%Y-%m')
        months.append(month)

        count = Committee.objects.filter(
            created_at__year=date.year,
            created_at__month=date.month
        ).count()

        data.append(count)

    return {
        'months': list(reversed(months)),
        'data': list(reversed(data)),
    }

def get_guarantees_by_month():
    data = []
    months = []

    for i in range(12):
        date = timezone.now() - timedelta(days=30 * i)
        month = date.strftime('%Y-%m')
        months.append(month)

        count = Guarantee.objects.filter(
            created_at__year=date.year,
            created_at__month=date.month
        ).count()

        data.append(count)

    return {
        'months': list(reversed(months)),
        'data': list(reversed(data)),
    }

@login_required
def notifications(request):
    notifications = request.user.notifications.all().order_by('-created_at')
    return render(request, 'core/notifications.html', {'notifications': notifications})

@login_required
def mark_notification_as_read(request, pk):
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.is_read = True
    notification.save()
    return redirect('core:notifications')

@login_required
def settings(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    settings = Setting.objects.all()
    return render(request, 'core/settings.html', {'settings': settings})

@login_required
def update_ui(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    if request.method == 'POST':
        primary_color = request.POST.get('primary_color')
        secondary_color = request.POST.get('secondary_color')
        system_name = request.POST.get('system_name')

        # تحديث الإعدادات
        Setting.objects.update_or_create(key='primary_color', defaults={'value': primary_color})
        Setting.objects.update_or_create(key='secondary_color', defaults={'value': secondary_color})
        Setting.objects.update_or_create(key='system_name', defaults={'value': system_name})

        # معالجة الشعار
        if 'logo' in request.FILES:
            logo = request.FILES['logo']
            Setting.objects.update_or_create(key='logo', defaults={'value': logo.name})

            # حفظ الشعار
            import os
            from django.conf import settings as django_settings
            logo_path = os.path.join(django_settings.MEDIA_ROOT, 'logo', logo.name)
            os.makedirs(os.path.dirname(logo_path), exist_ok=True)
            with open(logo_path, 'wb+') as destination:
                for chunk in logo.chunks():
                    destination.write(chunk)

        messages.success(request, 'تم تحديث الإعدادات بنجاح.')
        return redirect('core:settings')

    # الحصول على الإعدادات الحالية
    primary_color = Setting.objects.filter(key='primary_color').first()
    secondary_color = Setting.objects.filter(key='secondary_color').first()
    system_name = Setting.objects.filter(key='system_name').first()
    logo = Setting.objects.filter(key='logo').first()

    context = {
        'primary_color': primary_color.value if primary_color else '#007bff',
        'secondary_color': secondary_color.value if secondary_color else '#6c757d',
        'system_name': system_name.value if system_name else 'نظام إدارة الدائرة القانونية',
        'logo': logo.value if logo else None,
    }

    return render(request, 'core/update_ui.html', context)

@login_required
def backups(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    backups = Backup.objects.all().order_by('-created_at')
    return render(request, 'core/backups.html', {'backups': backups})

@login_required
def create_backup(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    if request.method == 'POST':
        name = request.POST.get('name')

        # إنشاء نسخة احتياطية
        import os
        import datetime
        import sys
        import traceback
        from django.conf import settings as django_settings
        from django.core.management import call_command

        try:
            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = os.path.join(django_settings.MEDIA_ROOT, 'backups')
            os.makedirs(backup_dir, exist_ok=True)

            # إنشاء ملف النسخة الاحتياطية
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{name.replace(' ', '_')}_{timestamp}.json"
            backup_path = os.path.join(backup_dir, backup_file)

            # تصدير قاعدة البيانات باستخدام ترميز UTF-8
            with open(backup_path, 'w', encoding='utf-8') as f:
                call_command('dumpdata', '--exclude', 'contenttypes', '--exclude', 'auth.permission',
                            '--exclude', 'sessions.session', '--indent', '2', stdout=f)

            # إنشاء سجل النسخة الاحتياطية
            backup = Backup.objects.create(
                name=name,
                file=f"backups/{backup_file}",
                created_by=request.user
            )

            messages.success(request, 'تم إنشاء النسخة الاحتياطية بنجاح.')
            return redirect('core:backups')

        except Exception as e:
            # حذف الملف إذا كان موجودًا
            if 'backup_path' in locals() and os.path.exists(backup_path):
                os.remove(backup_path)

            # تسجيل الخطأ وعرض رسالة للمستخدم
            error_msg = str(e)
            error_traceback = traceback.format_exc()
            print(f"Error creating backup: {error_msg}")
            print(error_traceback)

            messages.error(request, f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {error_msg}')
            return redirect('core:create_backup')

    return render(request, 'core/create_backup.html')

@login_required
def download_backup(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    backup = get_object_or_404(Backup, pk=pk)

    from django.http import FileResponse
    from django.conf import settings as django_settings
    import os

    file_path = os.path.join(django_settings.MEDIA_ROOT, str(backup.file))

    # استخدام ترميز UTF-8 عند قراءة الملف
    file = open(file_path, 'rb')
    response = FileResponse(file)
    response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
    response['Content-Type'] = 'application/json; charset=utf-8'
    return response

@login_required
def delete_backup(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    backup = get_object_or_404(Backup, pk=pk)

    # حذف ملف النسخة الاحتياطية
    from django.conf import settings as django_settings
    import os

    file_path = os.path.join(django_settings.MEDIA_ROOT, str(backup.file))
    if os.path.exists(file_path):
        os.remove(file_path)

    # حذف سجل النسخة الاحتياطية
    backup.delete()

    messages.success(request, 'تم حذف النسخة الاحتياطية بنجاح.')
    return redirect('core:backups')

@login_required
def dynamic_fields(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    fields = DynamicField.objects.all().order_by('section', 'order')
    return render(request, 'core/dynamic_fields.html', {'fields': fields})

@login_required
def create_dynamic_field(request):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = DynamicFieldForm(request.POST)
        if form.is_valid():
            field = form.save(commit=False)
            field.created_by = request.user
            field.save()
            messages.success(request, 'تم إنشاء الحقل بنجاح.')
            return redirect('core:dynamic_fields')
    else:
        form = DynamicFieldForm()

    return render(request, 'core/dynamic_field_form.html', {'form': form})

@login_required
def update_dynamic_field(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    field = get_object_or_404(DynamicField, pk=pk)

    if field.is_default and request.method == 'POST':
        # لا يمكن تغيير اسم الحقل البرمجي أو نوع الحقل للحقول الافتراضية
        post_data = request.POST.copy()
        post_data['name'] = field.name
        post_data['field_type'] = field.field_type
        post_data['section'] = field.section
        form = DynamicFieldForm(post_data, instance=field)
    elif request.method == 'POST':
        form = DynamicFieldForm(request.POST, instance=field)
    else:
        form = DynamicFieldForm(instance=field)

    if request.method == 'POST' and form.is_valid():
        form.save()
        messages.success(request, 'تم تحديث الحقل بنجاح.')
        return redirect('core:dynamic_fields')

    return render(request, 'core/dynamic_field_form.html', {'form': form, 'field': field})

@login_required
def delete_dynamic_field(request, pk):
    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية الوصول إلى هذه الصفحة.')
        return redirect('dashboard')

    field = get_object_or_404(DynamicField, pk=pk)

    if field.is_default:
        messages.error(request, 'لا يمكن حذف الحقول الافتراضية.')
        return redirect('core:dynamic_fields')

    if request.method == 'POST':
        # حذف جميع البيانات المرتبطة بهذا الحقل
        DynamicData.objects.filter(field=field).delete()
        field.delete()
        messages.success(request, 'تم حذف الحقل بنجاح.')
        return redirect('core:dynamic_fields')

    return render(request, 'core/dynamic_field_confirm_delete.html', {'field': field})

# وظيفة مساعدة لحفظ بيانات الحقول الديناميكية
def save_dynamic_data(form, instance, section):
    if not form.is_valid():
        return False

    content_type = ContentType.objects.get_for_model(instance)

    for field_name, value in form.cleaned_data.items():
        if field_name.startswith('dynamic_'):
            field_id = int(field_name.split('_')[1])
            field = DynamicField.objects.get(id=field_id)

            # التحقق من أن الحقل ينتمي للقسم الصحيح
            if field.section != section:
                continue

            # إنشاء أو تحديث البيانات
            dynamic_data, created = DynamicData.objects.get_or_create(
                field=field,
                content_type=content_type,
                object_id=instance.id
            )

            dynamic_data.set_value(value)
            dynamic_data.save()

    return True
