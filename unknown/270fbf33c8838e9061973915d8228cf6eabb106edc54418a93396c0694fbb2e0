{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تقارير الكفالات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تقارير الكفالات</h1>
    <div>
        <a href="{% url 'reports:index' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى التقارير
        </a>
        <div class="btn-group">
            <a href="{% url 'reports:export' 'guarantees' %}?export_type=csv{% if bank %}&bank={{ bank }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-csv"></i> تصدير CSV
            </a>
            <a href="{% url 'reports:export' 'guarantees' %}?export_type=excel{% if bank %}&bank={{ bank }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </a>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
            <div class="card-body">
                <h5 class="card-title">إجمالي الكفالات</h5>
                <p class="card-text display-4">{{ total_guarantees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
            <div class="card-body">
                <h5 class="card-title">الكفالات النشطة</h5>
                <p class="card-text display-4">{{ active_guarantees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger mb-3">
            <div class="card-body">
                <h5 class="card-title">الكفالات المنتهية</h5>
                <p class="card-text display-4">{{ expired_guarantees }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info mb-3">
            <div class="card-body">
                <h5 class="card-title">إجمالي المبالغ</h5>
                <p class="card-text">{{ total_amount }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تصفية التقرير</h5>
    </div>
    <div class="card-body">
        <form method="get" class="form-inline">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="bank">المصرف</label>
                    <select name="bank" id="bank" class="form-control w-100">
                        <option value="">الكل</option>
                        {% for bank_name in banks %}
                        <option value="{{ bank_name }}" {% if bank == bank_name %}selected{% endif %}>{{ bank_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="status">الحالة</label>
                    <select name="status" id="status" class="form-control w-100">
                        <option value="">الكل</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>نشطة</option>
                        <option value="expired" {% if status == 'expired' %}selected{% endif %}>منتهية</option>
                        <option value="expiring_soon" {% if status == 'expiring_soon' %}selected{% endif %}>ستنتهي قريباً</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from">من تاريخ</label>
                    <input type="date" name="date_from" id="date_from" class="form-control w-100" value="{{ date_from }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to">إلى تاريخ</label>
                    <input type="date" name="date_to" id="date_to" class="form-control w-100" value="{{ date_to }}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                    <a href="{% url 'reports:guarantees' %}" class="btn btn-secondary">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">نتائج التقرير</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستفيد</th>
                        <th>المبلغ</th>
                        <th>المصرف</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for guarantee in guarantees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ guarantee.beneficiary_name }}</td>
                        <td>{{ guarantee.amount }}</td>
                        <td>{{ guarantee.bank_name }}</td>
                        <td>{{ guarantee.start_date }}</td>
                        <td>{{ guarantee.end_date }}</td>
                        <td>
                            {% if guarantee.is_expired %}
                            <span class="badge badge-danger">منتهية</span>
                            {% elif guarantee.is_expiring_soon %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% else %}
                            <span class="badge badge-success">نشطة</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا توجد كفالات مطابقة لمعايير التصفية.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
