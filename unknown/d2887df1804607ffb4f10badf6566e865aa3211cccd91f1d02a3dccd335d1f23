from django.contrib import admin
from .models import File, Notification, Setting, Backup, DynamicField, DynamicData

@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    list_display = ('name', 'content_type', 'object_id', 'uploaded_by', 'uploaded_at')
    list_filter = ('content_type', 'uploaded_by')
    search_fields = ('name',)
    date_hierarchy = 'uploaded_at'

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'type', 'is_read', 'created_at')
    list_filter = ('type', 'is_read', 'user')
    search_fields = ('title', 'message')
    date_hierarchy = 'created_at'

@admin.register(Setting)
class SettingAdmin(admin.ModelAdmin):
    list_display = ('key', 'value', 'description')
    search_fields = ('key', 'value', 'description')

@admin.register(Backup)
class BackupAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_by', 'created_at')
    list_filter = ('created_by',)
    search_fields = ('name',)
    date_hierarchy = 'created_at'

@admin.register(DynamicField)
class DynamicFieldAdmin(admin.ModelAdmin):
    list_display = ('display_name', 'name', 'section', 'field_type', 'required', 'is_active', 'is_default', 'order')
    list_filter = ('section', 'field_type', 'required', 'is_active', 'is_default')
    search_fields = ('name', 'display_name')
    date_hierarchy = 'created_at'
    list_editable = ('order', 'is_active')

@admin.register(DynamicData)
class DynamicDataAdmin(admin.ModelAdmin):
    list_display = ('field', 'content_type', 'object_id', 'get_value', 'created_at')
    list_filter = ('field', 'content_type')
    date_hierarchy = 'created_at'
