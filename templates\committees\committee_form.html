{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}
{% if committee %}تعديل لجنة{% else %}إضافة لجنة جديدة{% endif %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if committee %}تعديل لجنة{% else %}إضافة لجنة جديدة{% endif %}</h1>
    <a href="{% url 'committees:list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى قائمة اللجان
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if committee %}تعديل لجنة{% else %}إضافة لجنة جديدة{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}">{{ form.title.label }}</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.title.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.order_number.id_for_label }}">{{ form.order_number.label }}</label>
                        {{ form.order_number }}
                        {% if form.order_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.order_number.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.order_date.id_for_label }}">{{ form.order_date.label }}</label>
                        {{ form.order_date }}
                        {% if form.order_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.order_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.type.id_for_label }}">{{ form.type.label }}</label>
                        {{ form.type }}
                        {% if form.type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.type.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.duration.id_for_label }}">{{ form.duration.label }}</label>
                        {{ form.duration }}
                        {% if form.duration.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.duration.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.end_date.id_for_label }}">{{ form.end_date.label }}</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="{{ form.responsible_entity.id_for_label }}">{{ form.responsible_entity.label }}</label>
                        {{ form.responsible_entity }}
                        {% if form.responsible_entity.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.responsible_entity.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- الحقول الجديدة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.chairman_rank.id_for_label }}">{{ form.chairman_rank.label }}</label>
                        {{ form.chairman_rank }}
                        {% if form.chairman_rank.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.chairman_rank.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.chairman_name.id_for_label }}">{{ form.chairman_name.label }}</label>
                        {{ form.chairman_name }}
                        {% if form.chairman_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.chairman_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.issuing_authority.id_for_label }}">{{ form.issuing_authority.label }}</label>
                        {{ form.issuing_authority }}
                        {% if form.issuing_authority.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.issuing_authority.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.file_number.id_for_label }}">{{ form.file_number.label }}</label>
                        {{ form.file_number }}
                        {% if form.file_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.file_number.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.progress_status.id_for_label }}">{{ form.progress_status.label }}</label>
                        {{ form.progress_status }}
                        {% if form.progress_status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.progress_status.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.violation_type.id_for_label }}">{{ form.violation_type.label }}</label>
                        {{ form.violation_type }}
                        {% if form.violation_type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.violation_type.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.accused_rank.id_for_label }}">{{ form.accused_rank.label }}</label>
                        {{ form.accused_rank }}
                        {% if form.accused_rank.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.accused_rank.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.council_fate.id_for_label }}">{{ form.council_fate.label }}</label>
                        {{ form.council_fate }}
                        {% if form.council_fate.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.council_fate.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.investigation_location.id_for_label }}">{{ form.investigation_location.label }}</label>
                        {{ form.investigation_location }}
                        {% if form.investigation_location.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.investigation_location.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="{{ form.last_action.id_for_label }}">{{ form.last_action.label }}</label>
                        {{ form.last_action }}
                        {% if form.last_action.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.last_action.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- أعضاء اللجنة -->
            <hr>
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">أعضاء اللجنة</h5>
                <div>
                    <span class="badge badge-info" id="members-count">0 أعضاء</span>
                    <button type="button" class="btn btn-success btn-sm" id="add-member-btn">
                        <i class="fas fa-plus"></i> إضافة عضو
                    </button>
                </div>
            </div>

            <div id="members-container">
                {{ formset.management_form }}
                <!-- سيتم إضافة الأعضاء هنا ديناميكياً -->
            </div>

            <div class="alert alert-info" id="members-info">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة:</strong> يجب أن تحتوي اللجنة على 3-7 أعضاء على الأقل.
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'committees:list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .member-form {
        background: #f8f9fa;
        border: 2px solid #e9ecef !important;
        transition: all 0.3s ease;
    }

    .member-form:hover {
        border-color: #007bff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .remove-member-btn {
        transition: all 0.3s ease;
    }

    .remove-member-btn:hover {
        transform: scale(1.1);
    }

    #add-member-btn {
        transition: all 0.3s ease;
    }

    #add-member-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    #members-count {
        font-size: 0.9rem;
        margin-right: 10px;
    }

    .member-form h6 {
        color: #007bff;
        font-weight: bold;
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // متغيرات عامة لإدارة الأعضاء
    let memberCount = 0;
    const maxMembers = 7;
    const minMembers = 3;

    // تحديث تاريخ الانتهاء بناءً على تاريخ البدء والمدة
    function updateEndDate() {
        const orderDateInput = document.getElementById('id_order_date');
        const durationInput = document.getElementById('id_duration');
        const endDateInput = document.getElementById('id_end_date');

        if (orderDateInput && durationInput && endDateInput) {
            const orderDate = new Date(orderDateInput.value);
            const duration = parseInt(durationInput.value);

            if (!isNaN(orderDate.getTime()) && !isNaN(duration)) {
                const endDate = new Date(orderDate);
                endDate.setDate(endDate.getDate() + duration);

                const year = endDate.getFullYear();
                const month = String(endDate.getMonth() + 1).padStart(2, '0');
                const day = String(endDate.getDate()).padStart(2, '0');

                endDateInput.value = `${year}-${month}-${day}`;
            }
        }
    }

    // إضافة عضو جديد
    function addMember() {
        if (memberCount >= maxMembers) {
            alert('لا يمكن إضافة أكثر من ' + maxMembers + ' أعضاء');
            return;
        }

        const memberIndex = memberCount;
        const memberHtml = `
            <div class="member-form border rounded p-3 mb-3" data-member-index="${memberIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0 text-primary">
                        <i class="fas fa-user"></i> عضو ${memberIndex + 1}
                    </h6>
                    <button type="button" class="btn btn-danger btn-sm remove-member-btn" onclick="removeMember(${memberIndex})">
                        <i class="fas fa-times"></i> حذف
                    </button>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_form-${memberIndex}-name">اسم العضو</label>
                            <input type="text" name="form-${memberIndex}-name" class="form-control" id="id_form-${memberIndex}-name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_form-${memberIndex}-role">الدور</label>
                            <select name="form-${memberIndex}-role" class="form-control" id="id_form-${memberIndex}-role" required>
                                <option value="">اختر الدور</option>
                                <option value="member">عضو</option>
                                <option value="chairman">رئيس اللجنة/المجلس</option>
                                <option value="rapporteur">مقرر</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_form-${memberIndex}-workplace">مقر العمل</label>
                            <input type="text" name="form-${memberIndex}-workplace" class="form-control" id="id_form-${memberIndex}-workplace">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="id_form-${memberIndex}-rank">الرتبة/العنوان الوظيفي</label>
                            <input type="text" name="form-${memberIndex}-rank" class="form-control" id="id_form-${memberIndex}-rank">
                        </div>
                    </div>
                </div>

                <input type="hidden" name="form-${memberIndex}-id" id="id_form-${memberIndex}-id">
                <input type="hidden" name="form-${memberIndex}-DELETE" id="id_form-${memberIndex}-DELETE">
            </div>
        `;

        document.getElementById('members-container').insertAdjacentHTML('beforeend', memberHtml);

        // إضافة تأثير الـ animation
        const newMemberForm = document.querySelector(`[data-member-index="${memberIndex}"]`);
        newMemberForm.classList.add('fade-in');

        // إضافة مستمع للتغيير في الدور
        const roleSelect = newMemberForm.querySelector('select[name*="-role"]');
        if (roleSelect) {
            roleSelect.addEventListener('change', updateMembersUI);
        }

        memberCount++;
        updateMembersUI();
    }

    // حذف عضو
    function removeMember(memberIndex) {
        const memberForm = document.querySelector(`[data-member-index="${memberIndex}"]`);
        if (memberForm) {
            memberForm.remove();
            memberCount--;
            updateMembersUI();
            reindexMembers();
        }
    }

    // إعادة ترقيم الأعضاء بعد الحذف
    function reindexMembers() {
        const memberForms = document.querySelectorAll('.member-form');
        memberForms.forEach((form, index) => {
            form.setAttribute('data-member-index', index);
            form.querySelector('h6').innerHTML = `<i class="fas fa-user"></i> عضو ${index + 1}`;
            form.querySelector('.remove-member-btn').setAttribute('onclick', `removeMember(${index})`);

            // تحديث أسماء الحقول
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                const id = input.getAttribute('id');
                if (name && name.includes('form-')) {
                    const newName = name.replace(/form-\d+-/, `form-${index}-`);
                    const newId = id.replace(/id_form-\d+-/, `id_form-${index}-`);
                    input.setAttribute('name', newName);
                    input.setAttribute('id', newId);
                }
            });

            // تحديث labels
            const labels = form.querySelectorAll('label');
            labels.forEach(label => {
                const forAttr = label.getAttribute('for');
                if (forAttr && forAttr.includes('id_form-')) {
                    const newFor = forAttr.replace(/id_form-\d+-/, `id_form-${index}-`);
                    label.setAttribute('for', newFor);
                }
            });
        });

        memberCount = memberForms.length;
    }

    // تحديث واجهة المستخدم
    function updateMembersUI() {
        const countBadge = document.getElementById('members-count');
        const addBtn = document.getElementById('add-member-btn');
        const totalFormsInput = document.getElementById('id_form-TOTAL_FORMS');

        countBadge.textContent = `${memberCount} أعضاء`;

        if (memberCount >= maxMembers) {
            addBtn.disabled = true;
            addBtn.innerHTML = '<i class="fas fa-ban"></i> الحد الأقصى';
        } else {
            addBtn.disabled = false;
            addBtn.innerHTML = '<i class="fas fa-plus"></i> إضافة عضو';
        }

        // تحديث عدد النماذج في Django formset
        if (totalFormsInput) {
            totalFormsInput.value = memberCount;
        }

        // تحديث رسالة التنبيه
        const infoAlert = document.getElementById('members-info');
        const chairmanCount = countChairmen();

        if (memberCount < minMembers) {
            infoAlert.className = 'alert alert-warning';
            infoAlert.innerHTML = `<i class="fas fa-exclamation-triangle"></i> <strong>تنبيه:</strong> يجب إضافة ${minMembers - memberCount} أعضاء إضافيين على الأقل.`;
        } else if (chairmanCount === 0) {
            infoAlert.className = 'alert alert-warning';
            infoAlert.innerHTML = `<i class="fas fa-exclamation-triangle"></i> <strong>تنبيه:</strong> يجب تعيين رئيس للجنة/المجلس.`;
        } else if (chairmanCount > 1) {
            infoAlert.className = 'alert alert-danger';
            infoAlert.innerHTML = `<i class="fas fa-times-circle"></i> <strong>خطأ:</strong> لا يمكن أن يكون هناك أكثر من رئيس واحد للجنة.`;
        } else {
            infoAlert.className = 'alert alert-success';
            infoAlert.innerHTML = `<i class="fas fa-check-circle"></i> <strong>ممتاز:</strong> عدد الأعضاء مناسب (${memberCount}/${maxMembers}) مع رئيس واحد.`;
        }
    }

    // عد عدد الرؤساء
    function countChairmen() {
        let count = 0;
        document.querySelectorAll('select[name*="-role"]').forEach(select => {
            if (select.value === 'chairman') {
                count++;
            }
        });
        return count;
    }

    // إضافة مستمع للتغييرات في الأدوار
    function addRoleChangeListeners() {
        document.querySelectorAll('select[name*="-role"]').forEach(select => {
            select.addEventListener('change', updateMembersUI);
        });
    }

    // تحميل الأعضاء الموجودين (في حالة التعديل)
    function loadExistingMembers() {
        const existingForms = document.querySelectorAll('.member-form');
        memberCount = existingForms.length;
        addRoleChangeListeners();
        updateMembersUI();
    }

    // الأحداث
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث تاريخ الانتهاء
        updateEndDate();

        // تحميل الأعضاء الموجودين
        loadExistingMembers();

        // زر إضافة عضو
        document.getElementById('add-member-btn').addEventListener('click', addMember);

        // إضافة أعضاء افتراضيين إذا لم يكن هناك أعضاء
        if (memberCount === 0) {
            addMember(); // رئيس اللجنة
            addMember(); // عضو
            addMember(); // عضو

            // تعيين الدور الافتراضي للعضو الأول كرئيس
            setTimeout(function() {
                const firstRoleSelect = document.querySelector('#id_form-0-role');
                if (firstRoleSelect) {
                    firstRoleSelect.value = 'chairman';
                }
            }, 100);
        }
    });

    // تحديث تاريخ الانتهاء عند تغيير تاريخ البدء أو المدة
    document.getElementById('id_order_date').addEventListener('change', updateEndDate);
    document.getElementById('id_duration').addEventListener('change', updateEndDate);
</script>
{% endblock %}
