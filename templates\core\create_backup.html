{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}إنشاء نسخة احتياطية جديدة{% endblock %}

{% block header_title %}إنشاء نسخة احتياطية جديدة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إنشاء نسخة احتياطية جديدة</h1>
    <div>
        <a href="{% url 'core:backups' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى النسخ الاحتياطية
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">إنشاء نسخة احتياطية جديدة</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            <div class="form-group">
                <label for="name">اسم النسخة الاحتياطية</label>
                <input type="text" class="form-control" id="name" name="name" pattern="[A-Za-z0-9_\-\. ]+" required>
                <small class="form-text text-muted">أدخل اسمًا وصفيًا للنسخة الاحتياطية (مثال: نسخة_قبل_التحديث)</small>
                <small class="form-text text-danger">ملاحظة: يرجى استخدام الأحرف الإنجليزية والأرقام والرموز (_-.) فقط في اسم النسخة الاحتياطية لتجنب مشاكل الترميز.</small>
            </div>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> تنبيه: سيتم إنشاء نسخة احتياطية كاملة لقاعدة البيانات. قد تستغرق هذه العملية بعض الوقت حسب حجم البيانات.
            </div>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> ملاحظة: في حالة حدوث خطأ أثناء إنشاء النسخة الاحتياطية، قد يكون السبب هو وجود بيانات تحتوي على أحرف خاصة. يمكنك تجربة استبعاد بعض التطبيقات أو استخدام أداة النسخ الاحتياطي الخاصة بقاعدة البيانات مباشرة.
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> إنشاء النسخة الاحتياطية
            </button>
        </form>
    </div>
</div>
{% endblock %}
