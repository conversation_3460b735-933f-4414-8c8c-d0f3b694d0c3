from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from .models import Guarantee
from .forms import GuaranteeForm, GuaranteeExtendForm, FileUploadForm
from core.models import File, Notification
from django.utils import timezone

@login_required
def guarantee_list(request):
    guarantees = Guarantee.objects.all()
    return render(request, 'guarantees/guarantee_list.html', {'guarantees': guarantees})

@login_required
def guarantee_detail(request, pk):
    guarantee = get_object_or_404(Guarantee, pk=pk)

    # الحصول على نوع المحتوى للكفالة
    guarantee_content_type = ContentType.objects.get_for_model(Guarantee)

    # الحصول على الملفات المرتبطة بالكفالة
    files = File.objects.filter(
        content_type=guarantee_content_type,
        object_id=guarantee.id
    )

    return render(request, 'guarantees/guarantee_detail.html', {
        'guarantee': guarantee,
        'files': files
    })

@login_required
def guarantee_create(request):
    if request.method == 'POST':
        form = GuaranteeForm(request.POST)
        if form.is_valid():
            guarantee = form.save(commit=False)
            guarantee.created_by = request.user
            guarantee.save()

            messages.success(request, 'تم إنشاء الكفالة بنجاح.')
            return redirect('guarantees:detail', pk=guarantee.pk)
    else:
        form = GuaranteeForm()

    return render(request, 'guarantees/guarantee_form.html', {'form': form})

@login_required
def guarantee_update(request, pk):
    guarantee = get_object_or_404(Guarantee, pk=pk)

    if guarantee.is_expired() and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تعديل كفالة منتهية.')
        return redirect('guarantees:detail', pk=guarantee.pk)

    if request.method == 'POST':
        form = GuaranteeForm(request.POST, instance=guarantee)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الكفالة بنجاح.')
            return redirect('guarantees:detail', pk=guarantee.pk)
    else:
        form = GuaranteeForm(instance=guarantee)

    return render(request, 'guarantees/guarantee_form.html', {'form': form, 'guarantee': guarantee})

@login_required
def guarantee_delete(request, pk):
    guarantee = get_object_or_404(Guarantee, pk=pk)

    if request.user.user_type != 'admin':
        messages.error(request, 'ليس لديك صلاحية حذف الكفالات.')
        return redirect('guarantees:detail', pk=guarantee.pk)

    if request.method == 'POST':
        guarantee.delete()
        messages.success(request, 'تم حذف الكفالة بنجاح.')
        return redirect('guarantees:list')

    return render(request, 'guarantees/guarantee_confirm_delete.html', {'guarantee': guarantee})

@login_required
def extend_guarantee(request, pk):
    guarantee = get_object_or_404(Guarantee, pk=pk)

    if guarantee.is_expired() and request.user.user_type != 'admin':
        messages.error(request, 'لا يمكن تمديد كفالة منتهية.')
        return redirect('guarantees:detail', pk=guarantee.pk)

    if request.method == 'POST':
        form = GuaranteeExtendForm(request.POST)
        if form.is_valid():
            days = form.cleaned_data['days']
            guarantee.extend(days)
            messages.success(request, f'تم تمديد الكفالة لمدة {days} يوم بنجاح.')
            return redirect('guarantees:detail', pk=guarantee.pk)
    else:
        form = GuaranteeExtendForm()

    return render(request, 'guarantees/guarantee_extend.html', {'form': form, 'guarantee': guarantee})

@login_required
def upload_document(request, pk):
    guarantee = get_object_or_404(Guarantee, pk=pk)

    if request.method == 'POST':
        form = FileUploadForm(request.POST, request.FILES)
        if form.is_valid():
            file = form.save(commit=False)
            file.content_type = ContentType.objects.get_for_model(Guarantee)
            file.object_id = guarantee.id
            file.uploaded_by = request.user
            file.save()
            messages.success(request, 'تم رفع الملف بنجاح.')
            return redirect('guarantees:detail', pk=guarantee.pk)
    else:
        form = FileUploadForm()

    return render(request, 'guarantees/file_upload.html', {'form': form, 'guarantee': guarantee})
