from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import json

class File(models.Model):
    name = models.CharField(_('اسم الملف'), max_length=100)
    file = models.FileField(_('الملف'), upload_to='files/%Y/%m/%d/')
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    uploaded_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='uploaded_files', verbose_name=_('تم الرفع بواسطة'))
    uploaded_at = models.DateTimeField(_('تاريخ الرفع'), auto_now_add=True)

    class Meta:
        verbose_name = _('ملف')
        verbose_name_plural = _('الملفات')
        ordering = ['-uploaded_at']

    def __str__(self):
        return self.name

class Notification(models.Model):
    NOTIFICATION_TYPE_CHOICES = (
        ('committee_expiry', _('انتهاء لجنة')),
        ('guarantee_expiry', _('انتهاء كفالة')),
        ('system', _('إشعار نظام')),
    )

    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='notifications', verbose_name=_('المستخدم'))
    title = models.CharField(_('عنوان الإشعار'), max_length=100)
    message = models.TextField(_('نص الإشعار'))
    type = models.CharField(_('نوع الإشعار'), max_length=20, choices=NOTIFICATION_TYPE_CHOICES)
    is_read = models.BooleanField(_('مقروء'), default=False)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('إشعار')
        verbose_name_plural = _('الإشعارات')
        ordering = ['-created_at']

    def __str__(self):
        return self.title

class Setting(models.Model):
    key = models.CharField(_('المفتاح'), max_length=100, unique=True)
    value = models.TextField(_('القيمة'))
    description = models.TextField(_('الوصف'), blank=True, null=True)

    class Meta:
        verbose_name = _('إعداد')
        verbose_name_plural = _('الإعدادات')
        ordering = ['key']

    def __str__(self):
        return self.key

class Backup(models.Model):
    name = models.CharField(_('اسم النسخة'), max_length=100)
    file = models.FileField(_('ملف النسخة'), upload_to='backups/')
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='backups', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)

    class Meta:
        verbose_name = _('نسخة احتياطية')
        verbose_name_plural = _('النسخ الاحتياطية')
        ordering = ['-created_at']

    def __str__(self):
        return self.name

class DynamicField(models.Model):
    FIELD_TYPE_CHOICES = (
        ('text', _('نص')),
        ('number', _('رقم')),
        ('date', _('تاريخ')),
        ('select', _('قائمة منسدلة')),
        ('textarea', _('نص طويل')),
        ('file', _('ملف')),
        ('boolean', _('نعم/لا')),
    )

    SECTION_CHOICES = (
        ('committees', _('اللجان والمجالس')),
        ('guarantees', _('الكفالات والاستشارات')),
    )

    section = models.CharField(_('القسم'), max_length=50, choices=SECTION_CHOICES)
    name = models.CharField(_('اسم الحقل البرمجي'), max_length=100)
    display_name = models.CharField(_('اسم العرض'), max_length=100)
    field_type = models.CharField(_('نوع الحقل'), max_length=20, choices=FIELD_TYPE_CHOICES)
    required = models.BooleanField(_('مطلوب'), default=False)
    options = models.TextField(_('الخيارات'), blank=True, null=True, help_text=_('للقوائم المنسدلة فقط، بتنسيق JSON'))
    order = models.PositiveIntegerField(_('الترتيب'), default=0)
    is_active = models.BooleanField(_('نشط'), default=True)
    is_default = models.BooleanField(_('حقل افتراضي'), default=False, help_text=_('الحقول الافتراضية لا يمكن حذفها'))
    created_by = models.ForeignKey('accounts.User', on_delete=models.SET_NULL, null=True, related_name='created_fields', verbose_name=_('تم الإنشاء بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('حقل ديناميكي')
        verbose_name_plural = _('الحقول الديناميكية')
        ordering = ['section', 'order']
        unique_together = ('section', 'name')

    def __str__(self):
        return f"{self.display_name} ({self.get_section_display()})"

    def get_options_dict(self):
        if not self.options:
            return {}
        try:
            return json.loads(self.options)
        except json.JSONDecodeError:
            return {}

class DynamicData(models.Model):
    field = models.ForeignKey(DynamicField, on_delete=models.CASCADE, related_name='data', verbose_name=_('الحقل'))
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    text_value = models.TextField(_('قيمة نصية'), blank=True, null=True)
    number_value = models.DecimalField(_('قيمة رقمية'), max_digits=15, decimal_places=2, blank=True, null=True)
    date_value = models.DateField(_('قيمة تاريخ'), blank=True, null=True)
    boolean_value = models.BooleanField(_('قيمة منطقية'), blank=True, null=True)
    file_value = models.FileField(_('قيمة ملف'), upload_to='dynamic_files/%Y/%m/%d/', blank=True, null=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('بيانات ديناميكية')
        verbose_name_plural = _('بيانات ديناميكية')
        unique_together = ('field', 'content_type', 'object_id')

    def __str__(self):
        return f"{self.field.display_name}: {self.get_value()}"

    def get_value(self):
        if self.field.field_type == 'text' or self.field.field_type == 'select' or self.field.field_type == 'textarea':
            return self.text_value
        elif self.field.field_type == 'number':
            return self.number_value
        elif self.field.field_type == 'date':
            return self.date_value
        elif self.field.field_type == 'boolean':
            return self.boolean_value
        elif self.field.field_type == 'file':
            return self.file_value
        return None

    def set_value(self, value):
        if self.field.field_type == 'text' or self.field.field_type == 'select' or self.field.field_type == 'textarea':
            self.text_value = value
        elif self.field.field_type == 'number':
            self.number_value = value
        elif self.field.field_type == 'date':
            self.date_value = value
        elif self.field.field_type == 'boolean':
            self.boolean_value = value
        elif self.field.field_type == 'file':
            self.file_value = value
