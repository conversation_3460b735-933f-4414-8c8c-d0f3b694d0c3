{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}تخصيص واجهة المستخدم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>تخصيص واجهة المستخدم</h1>
    <a href="{% url 'core:settings' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> العودة إلى الإعدادات
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تخصيص واجهة المستخدم</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="system_name">اسم النظام</label>
                        <input type="text" name="system_name" id="system_name" class="form-control" value="{{ system_name }}">
                        <small class="form-text text-muted">اسم النظام الذي سيظهر في العنوان والشعار</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="logo">الشعار</label>
                        <input type="file" name="logo" id="logo" class="form-control-file">
                        <small class="form-text text-muted">شعار النظام (اختياري)</small>
                        {% if logo %}
                        <div class="mt-2">
                            <img src="{{ MEDIA_URL }}logo/{{ logo }}" alt="الشعار الحالي" class="img-thumbnail" style="max-height: 100px;">
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="primary_color">اللون الرئيسي</label>
                        <input type="color" name="primary_color" id="primary_color" class="form-control" value="{{ primary_color }}">
                        <small class="form-text text-muted">اللون الرئيسي للنظام</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="secondary_color">اللون الثانوي</label>
                        <input type="color" name="secondary_color" id="secondary_color" class="form-control" value="{{ secondary_color }}">
                        <small class="form-text text-muted">اللون الثانوي للنظام</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ التغييرات
                </button>
                <a href="{% url 'core:settings' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">معاينة</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>الألوان الرئيسية</h6>
                <div class="d-flex mb-3">
                    <div class="p-3 mr-2" style="background-color: {{ primary_color }}; color: white; flex: 1;">اللون الرئيسي</div>
                    <div class="p-3" style="background-color: {{ secondary_color }}; color: white; flex: 1;">اللون الثانوي</div>
                </div>
            </div>
            <div class="col-md-6">
                <h6>الأزرار</h6>
                <button class="btn mr-2" style="background-color: {{ primary_color }}; color: white;">زر رئيسي</button>
                <button class="btn" style="background-color: {{ secondary_color }}; color: white;">زر ثانوي</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
