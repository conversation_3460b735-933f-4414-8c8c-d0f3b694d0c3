{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}قائمة الكفالات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>قائمة الكفالات</h1>
    <a href="{% url 'guarantees:create' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة كفالة جديدة
    </a>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">الكفالات والاستشارات</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المستفيد</th>
                        <th>المبلغ</th>
                        <th>المصرف</th>
                        <th>تاريخ البدء</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for guarantee in guarantees %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ guarantee.beneficiary_name }}</td>
                        <td>{{ guarantee.amount }}</td>
                        <td>{{ guarantee.bank_name }}</td>
                        <td>{{ guarantee.start_date }}</td>
                        <td>{{ guarantee.end_date }}</td>
                        <td>
                            {% if guarantee.is_expired %}
                            <span class="badge badge-danger">منتهية</span>
                            {% elif guarantee.is_expiring_soon %}
                            <span class="badge badge-warning">ستنتهي قريباً</span>
                            {% else %}
                            <span class="badge badge-success">نشطة</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{% url 'guarantees:detail' guarantee.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'guarantees:update' guarantee.id %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.user_type == 'admin' %}
                                <a href="{% url 'guarantees:delete' guarantee.id %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا توجد كفالات مسجلة.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
