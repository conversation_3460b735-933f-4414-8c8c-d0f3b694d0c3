/* تنسيقات مخصصة لشريط القائمة */

/* تنسيق أساسي لشريط القائمة */
.navbar {
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* شعار النظام */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
    margin-left: 1rem;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: white !important;
}

.navbar-brand i {
    margin-left: 0.5rem;
    font-size: 1.5rem;
}

/* تنسيق القائمة الرئيسية */
.navbar .navbar-nav {
    display: flex;
    align-items: center;
}

/* عناصر القائمة */
.navbar .nav-item {
    margin: 0;
}

.navbar .nav-link {
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.85) !important;
    position: relative;
}

.navbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
}

/* العنصر النشط */
.navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    color: white !important;
    font-weight: bold;
}

.navbar .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    height: 3px;
    background-color: white;
}

/* تأثير التحويم */
.navbar .nav-link.nav-link-hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
}

/* القوائم المنسدلة */
.navbar .dropdown-menu {
    right: 0;
    left: auto;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 0.5rem 0;
    margin-top: 0;
    border-top: none;
}

.navbar .dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
    position: relative;
}

.navbar .dropdown-item i {
    margin-left: 0.5rem;
    width: 1.25rem;
    text-align: center;
    color: #007bff;
}

.navbar .dropdown-item:hover {
    background-color: #f8f9fa;
}

.navbar .dropdown-item:hover::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #007bff;
}

/* قائمة المستخدم والإشعارات */
.navbar-user-section {
    display: flex;
    align-items: center;
    margin-right: auto;
}

/* تنسيق الإشعارات */
.notifications-dropdown .dropdown-menu {
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f1f1;
    transition: all 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

/* شارة الإشعارات */
.badge-notification {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
}

/* تنسيق أيقونات القائمة */
.navbar .nav-link i {
    margin-left: 0.5rem;
    width: 1.25rem;
    text-align: center;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 992px) {
    .navbar-user-section {
        margin-right: 0;
        margin-top: 1rem;
        width: 100%;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 0.5rem;
    }

    .navbar .nav-item {
        margin: 0.25rem 0;
    }

    .navbar .nav-link {
        padding: 0.75rem 1rem;
    }

    .navbar .nav-link.active::after {
        display: none;
    }

    .navbar .dropdown-menu {
        border: none;
        background-color: rgba(0, 0, 0, 0.05);
        box-shadow: none;
    }
}
